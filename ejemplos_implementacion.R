# =============================================================================
# EJEMPLOS PRÁCTICOS DE IMPLEMENTACIÓN - DOCUMENTACIÓN ROXYGEN2
# =============================================================================
# 
# Este archivo contiene ejemplos prácticos de cómo implementar la documentación
# Roxygen2 en tu proyecto R-Exams ICFES existente.
#
# Autor: Scout AI para Alvaretto
# Fecha: Julio 2025
# =============================================================================

# =============================================================================
# EJEMPLO 1: INTEGRAR DOCUMENTACIÓN EN ARCHIVO EXISTENTE
# =============================================================================

# Si tienes un archivo como "tests-unitarios-01.R", así es como agregar Roxygen2:

# ANTES (código original):
# library(testthat)
# generar_datos <- function() {
#   duracion <- sample(2:4, 1)
#   rapidez <- sample(30:100, 1)
#   # ... resto del código
# }

# DESPUÉS (con documentación Roxygen2):

#' Generar datos aleatorios para ejercicio de movimiento lineal
#'
#' @description
#' Función que genera parámetros aleatorios para ejercicios ICFES de movimiento
#' rectilíneo uniforme, garantizando consistencia matemática y pedagógica.
#'
#' @details
#' Esta función implementa el algoritmo core de aleatorización del sistema.
#' Genera valores que cumplen con las restricciones:
#' \itemize{
#'   \item distancia = rapidez × duración
#'   \item pos_final = pos_inicial + distancia  
#'   \item hora_final = hora_inicial + duración
#'   \item Todos los valores son enteros
#'   \item Rangos apropiados para nivel ICFES
#' }
#'
#' @param duracion_min Duración mínima en horas (default: 2)
#' @param duracion_max Duración máxima en horas (default: 4) 
#' @param rapidez_min Rapidez mínima en km/h (default: 30)
#' @param rapidez_max Rapidez máxima en km/h (default: 100)
#' @param pos_min Posición inicial mínima en km (default: 10)
#' @param pos_max Posición inicial máxima en km (default: 50)
#' @param hora_min Hora inicial mínima (default: 6)
#' @param hora_max Hora inicial máxima (default: 10)
#'
#' @return Lista con elementos:
#' \describe{
#'   \item{duracion}{Duración del viaje en horas (entero)}
#'   \item{rapidez}{Rapidez constante en km/h (entero)}
#'   \item{distancia}{Distancia recorrida en km (entero)}
#'   \item{pos_inicial}{Posición inicial en km (entero)}
#'   \item{pos_final}{Posición final en km (entero)}
#'   \item{hora_inicial}{Hora de inicio (entero)}
#'   \item{hora_final}{Hora de finalización (entero)}
#' }
#'
#' @section Algoritmo:
#' \enumerate{
#'   \item Generar duración aleatoria en rango especificado
#'   \item Generar rapidez aleatoria en rango especificado
#'   \item Ajustar rapidez para que distancia sea múltiplo exacto de duración
#'   \item Calcular distancia = rapidez × duración
#'   \item Generar posición inicial aleatoria
#'   \item Calcular posición final = posición inicial + distancia
#'   \item Generar hora inicial aleatoria
#'   \item Calcular hora final = hora inicial + duración
#' }
#'
#' @section Validaciones:
#' La función garantiza automáticamente:
#' \itemize{
#'   \item Consistencia matemática de todas las relaciones
#'   \item Valores enteros para facilitar cálculos
#'   \item Rangos apropiados para contexto educativo
#'   \item Reproducibilidad con semillas
#' }
#'
#' @examples
#' \dontrun{
#' # Uso básico con parámetros por defecto
#' datos <- generar_datos()
#' print(datos$distancia)  # Distancia calculada
#' print(datos$rapidez)    # Rapidez utilizada
#' 
#' # Uso con parámetros personalizados
#' datos <- generar_datos(
#'   duracion_min = 3, 
#'   duracion_max = 5,
#'   rapidez_min = 50, 
#'   rapidez_max = 120
#' )
#' 
#' # Verificar consistencia matemática
#' stopifnot(datos$distancia == datos$rapidez * datos$duracion)
#' stopifnot(datos$pos_final == datos$pos_inicial + datos$distancia)
#' stopifnot(datos$hora_final == datos$hora_inicial + datos$duracion)
#' 
#' # Generar múltiples versiones
#' set.seed(123)
#' versiones <- replicate(10, generar_datos(), simplify = FALSE)
#' distancias <- sapply(versiones, function(x) x$distancia)
#' print(range(distancias))
#' }
#'
#' @family funciones_generacion
#' @seealso \code{\link{validar_consistencia}}, \code{\link{ejecutar_tests_unitarios}}
#' <AUTHOR>
#' @export
generar_datos <- function(duracion_min = 2, duracion_max = 4,
                         rapidez_min = 30, rapidez_max = 100,
                         pos_min = 10, pos_max = 50,
                         hora_min = 6, hora_max = 10) {
  
  # Validar parámetros de entrada
  stopifnot("duracion_min debe ser menor que duracion_max" = duracion_min < duracion_max)
  stopifnot("rapidez_min debe ser menor que rapidez_max" = rapidez_min < rapidez_max)
  stopifnot("pos_min debe ser menor que pos_max" = pos_min < pos_max)
  stopifnot("hora_min debe ser menor que hora_max" = hora_min < hora_max)
  
  # Generar duración aleatoria
  duracion <- sample(duracion_min:duracion_max, 1)
  
  # Generar rapidez aleatoria y ajustar para consistencia
  rapidez <- sample(rapidez_min:rapidez_max, 1)
  rapidez <- rapidez - (rapidez %% duracion)
  
  # Asegurar que rapidez esté en rango después del ajuste
  if (rapidez < rapidez_min) {
    rapidez <- rapidez + duracion
  }
  
  # Calcular distancia
  distancia <- rapidez * duracion
  
  # Generar posiciones
  pos_inicial <- sample(pos_min:pos_max, 1)
  pos_final <- pos_inicial + distancia
  
  # Generar horas
  hora_inicial <- sample(hora_min:hora_max, 1)
  hora_final <- hora_inicial + duracion
  
  # Retornar lista con todos los valores
  list(
    duracion = as.integer(duracion),
    rapidez = as.integer(rapidez),
    distancia = as.integer(distancia),
    pos_inicial = as.integer(pos_inicial),
    pos_final = as.integer(pos_final),
    hora_inicial = as.integer(hora_inicial),
    hora_final = as.integer(hora_final)
  )
}

# =============================================================================
# EJEMPLO 2: DOCUMENTAR FUNCIÓN DE VALIDACIÓN
# =============================================================================

#' Validar consistencia matemática de datos generados
#'
#' @description
#' Función de testing que verifica la consistencia matemática y pedagógica
#' de los datos generados para ejercicios ICFES de movimiento lineal.
#'
#' @details
#' Esta función implementa las validaciones críticas del sistema de calidad.
#' Verifica que los datos generados cumplan con todos los requisitos
#' matemáticos, pedagógicos y técnicos para uso en exámenes oficiales.
#' 
#' Las validaciones incluyen:
#' \itemize{
#'   \item Consistencia de fórmulas físicas
#'   \item Integridad de tipos de datos
#'   \item Rangos apropiados para nivel educativo
#'   \item Ausencia de valores anómalos
#' }
#'
#' @param datos Lista con datos a validar, típicamente output de \code{generar_datos()}
#' @param verbose Lógico. Si TRUE, muestra detalles de cada validación
#' @param strict_mode Lógico. Si TRUE, aplica validaciones más estrictas
#'
#' @return Lista con resultados de validación:
#' \describe{
#'   \item{valid}{Lógico. TRUE si todas las validaciones pasan}
#'   \item{tests_passed}{Número de tests que pasaron exitosamente}
#'   \item{tests_total}{Número total de tests ejecutados}
#'   \item{errors}{Vector de errores encontrados (vacío si valid=TRUE)}
#'   \item{warnings}{Vector de advertencias no críticas}
#'   \item{details}{Lista detallada de cada validación si verbose=TRUE}
#' }
#'
#' @section Tests Implementados:
#' \subsection{Consistencia Matemática:}{
#'   \itemize{
#'     \item \code{distancia = rapidez × duración}
#'     \item \code{pos_final = pos_inicial + distancia}
#'     \item \code{hora_final = hora_inicial + duración}
#'     \item \code{distancia \%\% duración == 0}
#'   }
#' }
#' \subsection{Integridad de Datos:}{
#'   \itemize{
#'     \item Todos los valores son enteros
#'     \item No hay valores NA o NULL
#'     \item Estructura de datos correcta
#'   }
#' }
#' \subsection{Rangos Pedagógicos:}{
#'   \itemize{
#'     \item Duración: 2-4 horas (o personalizado)
#'     \item Rapidez: 30-100 km/h (o personalizado)
#'     \item Posiciones: 10-50 km (o personalizado)
#'     \item Horas: 6-10 (o personalizado)
#'   }
#' }
#'
#' @examples
#' \dontrun{
#' # Validación básica
#' datos <- generar_datos()
#' validacion <- validar_consistencia(datos)
#' 
#' if (validacion$valid) {
#'   cat("✅ Todos los tests pasaron\n")
#'   cat("📊 Tests exitosos:", validacion$tests_passed, "/", validacion$tests_total, "\n")
#' } else {
#'   cat("❌ Errores encontrados:\n")
#'   print(validacion$errors)
#' }
#' 
#' # Validación con detalles
#' validacion <- validar_consistencia(datos, verbose = TRUE)
#' print(validacion$details)
#' 
#' # Validación estricta
#' validacion <- validar_consistencia(datos, strict_mode = TRUE)
#' 
#' # Validación en lote
#' versiones <- replicate(100, generar_datos(), simplify = FALSE)
#' resultados <- lapply(versiones, validar_consistencia)
#' todas_validas <- all(sapply(resultados, function(x) x$valid))
#' cat("Validación en lote - Todas válidas:", todas_validas, "\n")
#' }
#'
#' @family funciones_validacion
#' @seealso \code{\link{generar_datos}}, \code{\link{ejecutar_tests_unitarios}}
#' <AUTHOR>
#' @export
validar_consistencia <- function(datos, verbose = FALSE, strict_mode = FALSE) {
  
  # Inicializar contadores
  tests_passed <- 0
  tests_total <- 0
  errors <- character(0)
  warnings <- character(0)
  details <- list()
  
  # Helper function para tests
  run_test <- function(test_name, condition, error_msg = NULL, warning_msg = NULL) {
    tests_total <<- tests_total + 1
    
    if (condition) {
      tests_passed <<- tests_passed + 1
      if (verbose) details[[test_name]] <<- "✅ PASS"
    } else {
      if (verbose) details[[test_name]] <<- "❌ FAIL"
      if (!is.null(error_msg)) {
        errors <<- c(errors, paste(test_name, ":", error_msg))
      }
      if (!is.null(warning_msg)) {
        warnings <<- c(warnings, paste(test_name, ":", warning_msg))
      }
    }
  }
  
  # Validar estructura de datos
  run_test(
    "Estructura de datos",
    is.list(datos) && length(datos) >= 7,
    "Datos deben ser una lista con al menos 7 elementos"
  )
  
  # Validar elementos requeridos
  elementos_requeridos <- c("duracion", "rapidez", "distancia", "pos_inicial", 
                           "pos_final", "hora_inicial", "hora_final")
  for (elem in elementos_requeridos) {
    run_test(
      paste("Elemento", elem, "presente"),
      elem %in% names(datos),
      paste("Elemento", elem, "faltante")
    )
  }
  
  # Validar que todos los valores sean enteros
  if (all(elementos_requeridos %in% names(datos))) {
    valores_numericos <- datos[elementos_requeridos]
    run_test(
      "Todos los valores son enteros",
      all(sapply(valores_numericos, function(x) is.integer(x) || (is.numeric(x) && x == as.integer(x)))),
      "No todos los valores son enteros"
    )
    
    # Validar ausencia de NA
    run_test(
      "Sin valores NA",
      !any(is.na(unlist(valores_numericos))),
      "Valores NA encontrados"
    )
    
    # Validaciones matemáticas
    run_test(
      "Distancia = rapidez × duración",
      datos$distancia == datos$rapidez * datos$duracion,
      paste("Distancia (", datos$distancia, ") ≠ rapidez × duración (", 
            datos$rapidez * datos$duracion, ")")
    )
    
    run_test(
      "Posición final = posición inicial + distancia", 
      datos$pos_final == datos$pos_inicial + datos$distancia,
      paste("pos_final (", datos$pos_final, ") ≠ pos_inicial + distancia (", 
            datos$pos_inicial + datos$distancia, ")")
    )
    
    run_test(
      "Hora final = hora inicial + duración",
      datos$hora_final == datos$hora_inicial + datos$duracion,
      paste("hora_final (", datos$hora_final, ") ≠ hora_inicial + duración (", 
            datos$hora_inicial + datos$duracion, ")")
    )
    
    run_test(
      "Distancia es múltiplo exacto de duración",
      datos$distancia %% datos$duracion == 0,
      paste("Distancia (", datos$distancia, ") no es múltiplo de duración (", datos$duracion, ")")
    )
    
    # Validaciones de rangos (modo estricto)
    if (strict_mode) {
      run_test(
        "Duración en rango [2,4]",
        datos$duracion >= 2 && datos$duracion <= 4,
        warning_msg = paste("Duración", datos$duracion, "fuera de rango típico [2,4]")
      )
      
      run_test(
        "Rapidez en rango [30,100]", 
        datos$rapidez >= 30 && datos$rapidez <= 100,
        warning_msg = paste("Rapidez", datos$rapidez, "fuera de rango típico [30,100]")
      )
      
      run_test(
        "Posición inicial en rango [10,50]",
        datos$pos_inicial >= 10 && datos$pos_inicial <= 50,
        warning_msg = paste("Posición inicial", datos$pos_inicial, "fuera de rango típico [10,50]")
      )
      
      run_test(
        "Hora inicial en rango [6,10]",
        datos$hora_inicial >= 6 && datos$hora_inicial <= 10,
        warning_msg = paste("Hora inicial", datos$hora_inicial, "fuera de rango típico [6,10]")
      )
    }
  }
  
  # Construir resultado
  resultado <- list(
    valid = length(errors) == 0,
    tests_passed = tests_passed,
    tests_total = tests_total,
    errors = errors,
    warnings = warnings
  )
  
  if (verbose) {
    resultado$details <- details
  }
  
  return(resultado)
}

# =============================================================================
# EJEMPLO 3: DOCUMENTAR FUNCIÓN DE TESTING UNITARIO
# =============================================================================

#' Ejecutar suite completa de tests unitarios
#'
#' @description
#' Función que ejecuta todos los tests unitarios del sistema R-Exams ICFES
#' para verificar integridad y calidad del software antes de uso en producción.
#'
#' @details
#' Esta función coordina la ejecución de todos los tests del sistema,
#' incluyendo tests de generación, validación, consistencia matemática,
#' y calidad pedagógica. Proporciona un reporte comprehensivo del estado
#' del sistema.
#'
#' @param archivo_rmd Ruta al archivo .Rmd a testear. Si NULL, usa archivo por defecto
#' @param num_iteraciones Número de iteraciones para tests estocásticos (default: 100)
#' @param incluir_benchmarks Lógico. Si TRUE, incluye mediciones de performance
#' @param modo_silencioso Lógico. Si TRUE, suprime salida detallada
#' @param semilla_base Semilla base para reproducibilidad (default: 123)
#'
#' @return Lista con resultados comprehensivos:
#' \describe{
#'   \item{resumen}{Resumen ejecutivo de todos los tests}
#'   \item{tests_individuales}{Resultados detallados de cada test}
#'   \item{estadisticas}{Estadísticas de performance y calidad}
#'   \item{benchmarks}{Resultados de benchmarks si incluir_benchmarks=TRUE}
#'   \item{recomendaciones}{Lista de recomendaciones basadas en resultados}
#' }
#'
#' @section Tests Incluidos:
#' \subsection{Tests de Generación:}{
#'   \itemize{
#'     \item Generación exitosa de datos
#'     \item Diversidad de parámetros generados
#'     \item Consistencia entre iteraciones
#'     \item Tiempo de ejecución aceptable
#'   }
#' }
#' \subsection{Tests de Validación:}{
#'   \itemize{
#'     \item Consistencia matemática en todas las iteraciones
#'     \item Validación de rangos pedagógicos
#'     \item Integridad de tipos de datos
#'     \item Manejo de casos edge
#'   }
#' }
#' \subsection{Tests de Calidad:}{
#'   \itemize{
#'     \item Unicidad de versiones generadas
#'     \item Distribución apropiada de parámetros
#'     \item Ausencia de sesgos sistemáticos
#'     \item Cobertura de casos de uso
#'   }
#' }
#'
#' @examples
#' \dontrun{
#' # Ejecución básica de tests
#' resultados <- ejecutar_tests_unitarios()
#' print(resultados$resumen)
#' 
#' # Tests con archivo específico
#' resultados <- ejecutar_tests_unitarios(
#'   archivo_rmd = "ejercicio_especifico.Rmd",
#'   num_iteraciones = 50
#' )
#' 
#' # Tests completos con benchmarks
#' resultados <- ejecutar_tests_unitarios(
#'   num_iteraciones = 1000,
#'   incluir_benchmarks = TRUE,
#'   modo_silencioso = FALSE
#' )
#' 
#' if (resultados$resumen$todos_exitosos) {
#'   cat("🎉 Todos los tests pasaron exitosamente!\n")
#' } else {
#'   cat("⚠️ Algunos tests fallaron. Ver detalles:\n")
#'   print(resultados$tests_individuales$fallos)
#' }
#' 
#' # Ver recomendaciones
#' if (length(resultados$recomendaciones) > 0) {
#'   cat("📋 Recomendaciones:\n")
#'   for (rec in resultados$recomendaciones) {
#'     cat("•", rec, "\n")
#'   }
#' }
#' }
#'
#' @family funciones_testing
#' @seealso \code{\link{generar_datos}}, \code{\link{validar_consistencia}}
#' <AUTHOR>
#' @export
ejecutar_tests_unitarios <- function(archivo_rmd = NULL,
                                   num_iteraciones = 100,
                                   incluir_benchmarks = FALSE,
                                   modo_silencioso = FALSE,
                                   semilla_base = 123) {
  
  if (!modo_silencioso) {
    cat("🧪 Iniciando suite de tests unitarios...\n")
    cat("📊 Iteraciones:", num_iteraciones, "\n")
    cat("🎯 Archivo:", ifelse(is.null(archivo_rmd), "default", archivo_rmd), "\n\n")
  }
  
  # Establecer semilla para reproducibilidad
  set.seed(semilla_base)
  
  # Inicializar contadores
  tests_exitosos <- 0
  tests_totales <- 0
  tiempo_inicio <- Sys.time()
  
  # Contenedores para resultados
  resultados_individuales <- list()
  estadisticas <- list()
  benchmarks <- list()
  recomendaciones <- character(0)
  
  # Test 1: Generación básica de datos
  if (!modo_silencioso) cat("🔄 Test 1: Generación básica de datos...\n")
  
  test1_exitoso <- tryCatch({
    datos_test <- generar_datos()
    validacion_test <- validar_consistencia(datos_test)
    validacion_test$valid
  }, error = function(e) FALSE)
  
  tests_totales <- tests_totales + 1
  if (test1_exitoso) tests_exitosos <- tests_exitosos + 1
  resultados_individuales$generacion_basica <- test1_exitoso
  
  # Test 2: Generación masiva y consistencia
  if (!modo_silencioso) cat("🔄 Test 2: Generación masiva y consistencia...\n")
  
  test2_exitoso <- tryCatch({
    versiones <- replicate(num_iteraciones, generar_datos(), simplify = FALSE)
    validaciones <- lapply(versiones, validar_consistencia)
    todas_validas <- all(sapply(validaciones, function(x) x$valid))
    
    # Estadísticas de diversidad
    distancias <- sapply(versiones, function(x) x$distancia)
    rapideces <- sapply(versiones, function(x) x$rapidez)
    
    estadisticas$diversidad_distancias <<- length(unique(distancias))
    estadisticas$diversidad_rapideces <<- length(unique(rapideces))
    estadisticas$rango_distancias <<- range(distancias)
    estadisticas$rango_rapideces <<- range(rapideces)
    
    todas_validas && estadisticas$diversidad_distancias > num_iteraciones * 0.5
  }, error = function(e) FALSE)
  
  tests_totales <- tests_totales + 1
  if (test2_exitoso) tests_exitosos <- tests_exitosos + 1
  resultados_individuales$generacion_masiva <- test2_exitoso
  
  # Test 3: Unicidad de hashes (si se implementa la función)
  if (!modo_silencioso) cat("🔄 Test 3: Unicidad de versiones...\n")
  
  test3_exitoso <- tryCatch({
    # Simular generación de hashes únicos
    hashes <- sapply(1:min(50, num_iteraciones), function(i) {
      datos <- generar_datos()
      digest::digest(list(datos, i, Sys.time()))
    })
    
    unicidad <- length(unique(hashes)) == length(hashes)
    estadisticas$unicidad_versiones <<- unicidad
    
    unicidad
  }, error = function(e) FALSE)
  
  tests_totales <- tests_totales + 1
  if (test3_exitoso) tests_exitosos <- tests_exitosos + 1
  resultados_individuales$unicidad_versiones <- test3_exitoso
  
  # Benchmarks opcionales
  if (incluir_benchmarks) {
    if (!modo_silencioso) cat("⏱️ Ejecutando benchmarks...\n")
    
    # Benchmark de generación
    tiempo_generacion <- system.time({
      replicate(100, generar_datos())
    })
    
    benchmarks$tiempo_generacion_100 <- tiempo_generacion["elapsed"]
    benchmarks$generaciones_por_segundo <- 100 / tiempo_generacion["elapsed"]
    
    # Benchmark de validación
    datos_benchmark <- generar_datos()
    tiempo_validacion <- system.time({
      replicate(100, validar_consistencia(datos_benchmark))
    })
    
    benchmarks$tiempo_validacion_100 <- tiempo_validacion["elapsed"]
    benchmarks$validaciones_por_segundo <- 100 / tiempo_validacion["elapsed"]
  }
  
  # Generar recomendaciones
  if (tests_exitosos < tests_totales) {
    recomendaciones <- c(recomendaciones, 
                        "Revisar tests fallidos y corregir funciones correspondientes")
  }
  
  if (incluir_benchmarks && benchmarks$generaciones_por_segundo < 1000) {
    recomendaciones <- c(recomendaciones,
                        "Considerar optimización de funciones de generación")
  }
  
  if (estadisticas$diversidad_distancias < num_iteraciones * 0.7) {
    recomendaciones <- c(recomendaciones,
                        "Aumentar diversidad de parámetros generados")
  }
  
  # Calcular tiempo total
  tiempo_total <- as.numeric(difftime(Sys.time(), tiempo_inicio, units = "secs"))
  
  # Construir resumen
  resumen <- list(
    todos_exitosos = tests_exitosos == tests_totales,
    tests_exitosos = tests_exitosos,
    tests_totales = tests_totales,
    porcentaje_exito = round(tests_exitosos / tests_totales * 100, 1),
    tiempo_total_segundos = tiempo_total,
    iteraciones_ejecutadas = num_iteraciones
  )
  
  # Resultado final
  resultado <- list(
    resumen = resumen,
    tests_individuales = resultados_individuales,
    estadisticas = estadisticas,
    benchmarks = if (incluir_benchmarks) benchmarks else NULL,
    recomendaciones = recomendaciones
  )
  
  # Mostrar resumen final
  if (!modo_silencioso) {
    cat("\n" , rep("=", 50), "\n")
    cat("📋 RESUMEN DE TESTS UNITARIOS\n")
    cat(rep("=", 50), "\n")
    cat("✅ Tests exitosos:", tests_exitosos, "/", tests_totales, 
        "(", resumen$porcentaje_exito, "%)\n")
    cat("⏱️ Tiempo total:", round(tiempo_total, 2), "segundos\n")
    cat("🔢 Iteraciones:", num_iteraciones, "\n")
    
    if (resumen$todos_exitosos) {
      cat("🎉 TODOS LOS TESTS PASARON EXITOSAMENTE!\n")
    } else {
      cat("⚠️ ALGUNOS TESTS FALLARON - REVISAR DETALLES\n")
    }
    
    if (length(recomendaciones) > 0) {
      cat("\n📋 Recomendaciones:\n")
      for (i in seq_along(recomendaciones)) {
        cat(paste0(i, ". ", recomendaciones[i], "\n"))
      }
    }
    cat(rep("=", 50), "\n")
  }
  
  return(resultado)
}

# =============================================================================
# EJEMPLO 4: FUNCIÓN DE UTILIDAD CON DOCUMENTACIÓN MÍNIMA
# =============================================================================

#' Generar reporte de calidad del sistema
#'
#' @description Genera un reporte comprehensivo de la calidad del sistema R-Exams ICFES
#' @param incluir_graficos Lógico. Si TRUE, incluye gráficos de distribución
#' @param archivo_salida Ruta donde guardar el reporte (opcional)
#' @return Lista con métricas de calidad del sistema
#' @examples
#' \dontrun{
#' reporte <- generar_reporte_calidad()
#' print(reporte$metricas_principales)
#' }
#' @export
generar_reporte_calidad <- function(incluir_graficos = FALSE, archivo_salida = NULL) {
  
  # Ejecutar tests comprehensivos
  resultados_tests <- ejecutar_tests_unitarios(num_iteraciones = 200, 
                                              incluir_benchmarks = TRUE,
                                              modo_silencioso = TRUE)
  
  # Generar métricas adicionales
  versiones_muestra <- replicate(50, generar_datos(), simplify = FALSE)
  
  metricas <- list(
    calidad_tests = resultados_tests$resumen$porcentaje_exito,
    diversidad_promedio = mean(c(
      length(unique(sapply(versiones_muestra, function(x) x$distancia))),
      length(unique(sapply(versiones_muestra, function(x) x$rapidez)))
    )),
    performance_generacion = resultados_tests$benchmarks$generaciones_por_segundo,
    performance_validacion = resultados_tests$benchmarks$validaciones_por_segundo,
    timestamp = Sys.time()
  )
  
  reporte <- list(
    metricas_principales = metricas,
    resultados_tests = resultados_tests,
    versiones_muestra = if (incluir_graficos) versiones_muestra else NULL,
    recomendaciones_calidad = resultados_tests$recomendaciones
  )
  
  # Guardar si se especifica archivo
  if (!is.null(archivo_salida)) {
    saveRDS(reporte, archivo_salida)
  }
  
  return(reporte)
}

# =============================================================================
# INSTRUCCIONES DE USO
# =============================================================================

cat("📚 EJEMPLOS DE IMPLEMENTACIÓN CARGADOS\n")
cat("=====================================\n\n")

cat("🔧 Para implementar en tu proyecto:\n")
cat("1. Copia las funciones documentadas a tus archivos .R\n")
cat("2. Ejecuta devtools::document() para generar documentación\n")
cat("3. Usa ?nombre_funcion para ver la documentación\n\n")

cat("🧪 Para probar los ejemplos:\n")
cat("1. datos <- generar_datos()\n")
cat("2. validacion <- validar_consistencia(datos, verbose = TRUE)\n")
cat("3. tests <- ejecutar_tests_unitarios(num_iteraciones = 10)\n")
cat("4. reporte <- generar_reporte_calidad()\n\n")

cat("📖 Documentación disponible:\n")
cat("• ?generar_datos\n")
cat("• ?validar_consistencia  \n")
cat("• ?ejecutar_tests_unitarios\n")
cat("• ?generar_reporte_calidad\n\n")

cat("✨ ¡Tu proyecto ahora tiene documentación profesional!\n")