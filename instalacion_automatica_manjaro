#!/bin/bash

# instalacion_automatica_manjaro
# Script de instalación automatizada para ICFESMathExams en Manjaro Plasma

echo "🚀 Instalación Automatizada - ICFESMathExams en Manjaro Plasma"
echo "=============================================================="
echo "Este script instalará automáticamente todos los prerrequisitos"
echo "y el paquete ICFESMathExams en tu sistema Manjaro."
echo ""

# Función para mostrar progreso
mostrar_progreso() {
    echo ""
    echo "⏳ $1..."
    sleep 1
}

# Función para verificar éxito de comando
verificar_exito() {
    if [ $? -eq 0 ]; then
        echo "✅ $1 completado exitosamente"
    else
        echo "❌ Error en: $1"
        echo "🛑 Instalación abortada. Revisa el error anterior."
        exit 1
    fi
}

# Verificar si se ejecuta como usuario normal (no root)
if [ "$EUID" -eq 0 ]; then
    echo "❌ No ejecutes este script como root (sudo)"
    echo "💡 Ejecuta: bash instalacion_automatica_manjaro"
    exit 1
fi

# Verificar si existe el archivo del paquete
if [ ! -f "ICFESMathExams_v1.0.0.tar.gz" ]; then
    echo "❌ No se encontró ICFESMathExams_v1.0.0.tar.gz en el directorio actual"
    echo "💡 Asegúrate de que el archivo esté en el mismo directorio que este script"
    echo "📁 Directorio actual: $(pwd)"
    exit 1
fi

echo "📋 Verificaciones iniciales completadas"
echo "🔍 Archivo del paquete encontrado: ICFESMathExams_v1.0.0.tar.gz"
echo ""

# Paso 1: Actualizar el sistema
mostrar_progreso "Actualizando el sistema"
sudo pacman -Syu --noconfirm
verificar_exito "Actualización del sistema"

# Paso 2: Instalar dependencias del sistema
mostrar_progreso "Instalando dependencias del sistema"
sudo pacman -S --needed --noconfirm \
    r \
    gcc-fortran \
    blas \
    lapack \
    git \
    curl \
    wget \
    make \
    base-devel \
    texlive-core \
    texlive-latexextra \
    texlive-fontsextra \
    libxml2 \
    openssl \
    pkg-config
verificar_exito "Instalación de dependencias del sistema"

# Paso 3: Verificar instalación de R
mostrar_progreso "Verificando instalación de R"
if command -v R &> /dev/null; then
    R_VERSION=$(R --version | head -1)
    echo "✅ $R_VERSION"
else
    echo "❌ R no se instaló correctamente"
    exit 1
fi

# Paso 4: Crear directorio de trabajo
mostrar_progreso "Configurando directorio de trabajo"
WORK_DIR="$HOME/R/ICFESMathExams"
mkdir -p "$WORK_DIR"
cd "$WORK_DIR"
echo "📁 Directorio de trabajo: $WORK_DIR"

# Paso 5: Copiar y extraer el paquete
mostrar_progreso "Preparando archivos del paquete"
cp "$OLDPWD/ICFESMathExams_v1.0.0.tar.gz" .
tar -xzf ICFESMathExams_v1.0.0.tar.gz
verificar_exito "Extracción del paquete"

# Paso 6: Instalar dependencias R
mostrar_progreso "Instalando dependencias R (esto puede tomar varios minutos)"
cat > instalar_deps_r.R << 'EOF'
options(repos = c(CRAN = "https://cloud.r-project.org/"))

dependencias <- c(
  "devtools", "testthat", "roxygen2", "knitr", "rmarkdown",
  "ggplot2", "dplyr", "readr", "stringr", "purrr", 
  "tibble", "magrittr", "jsonlite", "yaml", "DT"
)

cat("📦 Instalando", length(dependencias), "dependencias R...\n")

for (i in seq_along(dependencias)) {
  pkg <- dependencias[i]
  cat(sprintf("[%d/%d] Instalando %s...\n", i, length(dependencias), pkg))
  
  if (!require(pkg, character.only = TRUE, quietly = TRUE)) {
    install.packages(pkg, dependencies = TRUE, quiet = TRUE)
    if (!require(pkg, character.only = TRUE, quietly = TRUE)) {
      stop("Error instalando ", pkg)
    }
  }
}

cat("✅ Todas las dependencias R instaladas exitosamente\n")
EOF

Rscript instalar_deps_r.R
verificar_exito "Instalación de dependencias R"

# Paso 7: Verificar estructura del paquete
mostrar_progreso "Verificando estructura del paquete"
cd ICFESMathExams
Rscript verify.R
verificar_exito "Verificación de estructura"

# Paso 8: Construir e instalar ICFESMathExams
mostrar_progreso "Construyendo e instalando ICFESMathExams (esto puede tomar unos minutos)"
Rscript build.R
verificar_exito "Construcción e instalación del paquete"

# Paso 9: Verificar instalación
mostrar_progreso "Verificando instalación final"
cat > test_instalacion.R << 'EOF'
# Test completo de instalación
library(ICFESMathExams)

# Verificar funciones principales
funciones <- c("crear_configuracion_basica", "generar_datos_icfes", 
               "validar_datos_icfes", "exportar_resultados")

for (func in funciones) {
  if (!exists(func)) stop("Función no encontrada: ", func)
}

# Test básico de funcionalidad
config <- crear_configuracion_basica(
  nivel = "grado_11",
  areas = c("algebra"),
  num_ejercicios = 3,
  num_versiones = 1
)

cat("✅ ICFESMathExams instalado y funcionando correctamente!\n")
cat("📊 Versión instalada:", as.character(packageVersion("ICFESMathExams")), "\n")
EOF

Rscript test_instalacion.R
verificar_exito "Verificación final"

# Paso 10: Crear scripts de acceso rápido
mostrar_progreso "Configurando acceso rápido"
cat > "$HOME/icfes_math_exams.R" << 'EOF'
#!/usr/bin/env Rscript
# Script de acceso rápido a ICFESMathExams

library(ICFESMathExams)
cat("🎯 ICFESMathExams cargado exitosamente!\n")
cat("📚 Para ver la documentación: browseVignettes('ICFESMathExams')\n")
cat("🆘 Para ver ayuda: help(package = 'ICFESMathExams')\n")
cat("🧪 Para ejecutar ejemplo: source('ejemplo_basico.R')\n\n")
EOF

cat > "$HOME/ejemplo_basico.R" << 'EOF'
# Ejemplo básico de uso de ICFESMathExams
library(ICFESMathExams)

cat("🎯 Ejemplo básico de ICFESMathExams\n\n")

# Crear configuración
config <- crear_configuracion_basica(
  nivel = "grado_11",
  areas = c("algebra", "geometria"),
  num_ejercicios = 5,
  num_versiones = 2
)

cat("✅ Configuración creada\n")

# Generar datos
datos <- generar_datos_icfes(config)
cat("✅ Datos generados\n")

# Validar
validacion <- validar_datos_icfes(datos)
cat("✅ Validación completada\n")

cat("\n🎉 ¡Primer examen generado exitosamente!\n")
cat("📊 Para ver más ejemplos: browseVignettes('ICFESMathExams')\n")
EOF

chmod +x "$HOME/icfes_math_exams.R"
chmod +x "$HOME/ejemplo_basico.R"

# Resumen final
echo ""
echo "================================================"
echo "🎉 ¡INSTALACIÓN COMPLETADA EXITOSAMENTE!"
echo "================================================"
echo ""
echo "✅ ICFESMathExams v1.0.0 está instalado y listo para usar"
echo ""
echo "🚀 COMANDOS RÁPIDOS:"
echo "   Cargar paquete:     R -e 'library(ICFESMathExams)'"
echo "   Ver documentación:  R -e 'browseVignettes(\"ICFESMathExams\")'"
echo "   Ejecutar ejemplo:   Rscript ~/ejemplo_basico.R"
echo "   Acceso rápido:      Rscript ~/icfes_math_exams.R"
echo ""
echo "📚 ARCHIVOS CREADOS:"
echo "   Paquete instalado:  $WORK_DIR/ICFESMathExams/"
echo "   Script de ejemplo:  ~/ejemplo_basico.R"
echo "   Acceso rápido:      ~/icfes_math_exams.R"
echo ""
echo "📋 PRÓXIMOS PASOS:"
echo "   1. Ejecuta: Rscript ~/ejemplo_basico.R"
echo "   2. Explora: R -e 'browseVignettes(\"ICFESMathExams\")'"
echo "   3. Lee la documentación incluida en los vignettes"
echo ""
echo "🎯 ¡Listo para crear exámenes de matemáticas ICFES!"
echo "================================================"