# ICFESMathExams - Paquete Completo para Manjaro Plasma 🐧

## 📦 Contenido del Paquete

Este archivo contiene **todo lo necesario** para instalar y usar ICFESMathExams en Manjaro Plasma:

### 🎯 Pa<PERSON>e Principal
- **`ICFESMathExams/`** - Paquete R completo con 34 funciones, tests y documentación

### 📚 Guías de Instalación
- **`guia_instalacion_manjaro_completa.md`** - Guía paso a paso detallada
- **`comandos_rapidos_manjaro.md`** - Comandos express para instalación rápida
- **`troubleshooting_manjaro.md`** - Soluciones a problemas comunes

### 🛠️ Scripts Automatizados
- **`verificar_prerrequisitos_manjaro`** - Verifica que tu sistema esté listo
- **`instalacion_automatica_manjaro`** - Instala todo automáticamente

## 🚀 Instalación Rápida (5 Minutos)

### Opción 1: Automática (Recomendada)
```bash
# 1. Extraer archivos
tar -xzf ICFESMathExams_Manjaro_Complete.tar.gz
cd ICFESMathExams_Manjaro_Complete/

# 2. Verificar prerrequisitos
chmod +x verificar_prerrequisitos_manjaro
./verificar_prerrequisitos_manjaro

# 3. Instalación automática
chmod +x instalacion_automatica_manjaro
./instalacion_automatica_manjaro
```

### Opción 2: Manual Express
```bash
# 1. Instalar dependencias del sistema
sudo pacman -S r gcc-fortran blas lapack texlive-core

# 2. Extraer y instalar paquete
tar -xzf ICFESMathExams_Manjaro_Complete.tar.gz
cd ICFESMathExams_Manjaro_Complete/ICFESMathExams/

# 3. Construir e instalar
R -e "install.packages(c('devtools','testthat','roxygen2'), repos='https://cloud.r-project.org/')"
Rscript build.R

# 4. Verificar
R -e "library(ICFESMathExams); cat('✅ ¡Funcionando!\n')"
```

## ✅ Verificación Rápida

Después de la instalación, ejecuta:

```bash
R -e "
library(ICFESMathExams)
config <- crear_configuracion_basica(nivel='grado_11', areas='algebra', num_ejercicios=3, num_versiones=1)
datos <- generar_datos_icfes(config)
cat('🎉 ICFESMathExams funcionando correctamente!\n')
"
```

## 📖 Qué Hacer Después

1. **Explorar la documentación:**
   ```bash
   R -e "browseVignettes('ICFESMathExams')"
   ```

2. **Ver funciones disponibles:**
   ```bash
   R -e "help(package='ICFESMathExams')"
   ```

3. **Ejecutar ejemplo básico:**
   ```bash
   Rscript ~/ejemplo_basico.R
   ```

## 🆘 Si Tienes Problemas

1. **Lee primero:** `troubleshooting_manjaro.md`
2. **Verifica prerrequisitos:** `./verificar_prerrequisitos_manjaro`
3. **Usa la guía completa:** `guia_instalacion_manjaro_completa.md`

## 📊 Capacidades del Paquete

### Para Docentes 👨‍🏫
- Crear evaluaciones personalizadas
- Generar versiones múltiples automáticamente
- Validar calidad pedagógica

### Para Coordinadores 🏫
- Simulacros masivos ICFES
- Análisis estadísticos institucionales
- Reportes para directivos

### Para Universidades 🎓
- Exámenes de admisión
- Programas de nivelación
- Evaluación de competencias

### Para Investigadores 🔬
- Análisis psicométricos avanzados
- Estudios de equidad educativa
- Validación de instrumentos

## 🎯 Características Técnicas

- **34 Funciones R** documentadas con Roxygen2
- **20+ Tests Unitarios** con testthat
- **3 Vignettes Completas** (15,000+ palabras)
- **Análisis IRT** y Machine Learning
- **Exportación múltiple** (PDF, HTML, LaTeX, Word)
- **Integración LMS** (Moodle, Canvas, Blackboard)

## 📞 Soporte

- **Documentación completa** incluida en vignettes
- **Ejemplos prácticos** en casos de uso
- **Scripts de diagnóstico** automatizados
- **Guías específicas** para Manjaro Plasma

---

**🚀 ¡Listo para transformar la evaluación educativa en matemáticas!**

*ICFESMathExams v1.0.0 - Optimizado para Manjaro Plasma* 🐧