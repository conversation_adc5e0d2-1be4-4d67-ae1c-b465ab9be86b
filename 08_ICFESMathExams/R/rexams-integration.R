#' @title Integración Nativa con r-exams Framework
#' @description Funciones para integración completa con el ecosistema r-exams,
#' incluyendo generación de templates, exportación a múltiples formatos y
#' gestión de ejercicios compatibles con r-exams.

#' Crear template r-exams desde datos ICFESMathExams
#' 
#' Convierte datos generados por ICFESMathExams en templates .Rmd compatibles con r-exams
#' 
#' @param datos Lista con datos del ejercicio generado por ICFESMathExams
#' @param tipo_template Tipo de template ("single_choice", "multiple_choice", "numeric", "string")
#' @param directorio_salida Directorio donde guardar el template
#' @param nombre_archivo Nombre del archivo template (sin extensión)
#' @return Ruta del archivo template creado
#' @export
#' @examples
#' datos <- generar_datos("movimiento_lineal")
#' template_path <- crear_template_rexams(datos, "single_choice", "templates/")
crear_template_rexams <- function(datos, tipo_template = "single_choice", 
                                 directorio_salida = "templates/", 
                                 nombre_archivo = NULL) {
  
  # Validar entrada
  if (is.null(datos) || !is.list(datos)) {
    stop("Los datos deben ser una lista válida generada por ICFESMathExams")
  }
  
  # Crear directorio si no existe
  if (!dir.exists(directorio_salida)) {
    dir.create(directorio_salida, recursive = TRUE)
  }
  
  # Generar nombre de archivo si no se proporciona
  if (is.null(nombre_archivo)) {
    nombre_archivo <- paste0(datos$tipo, "_", format(Sys.time(), "%Y%m%d_%H%M%S"))
  }
  
  # Generar contenido del template según el tipo
  contenido_template <- switch(tipo_template,
    "single_choice" = generar_template_single_choice(datos),
    "multiple_choice" = generar_template_multiple_choice(datos),
    "numeric" = generar_template_numeric(datos),
    "string" = generar_template_string(datos),
    stop("Tipo de template no soportado: ", tipo_template)
  )
  
  # Escribir archivo
  ruta_completa <- file.path(directorio_salida, paste0(nombre_archivo, ".Rmd"))
  writeLines(contenido_template, ruta_completa)
  
  message("Template r-exams creado: ", ruta_completa)
  return(ruta_completa)
}

#' Generar template de opción múltiple
#' 
#' @param datos Lista con datos del ejercicio
#' @return Vector de caracteres con contenido del template
#' @keywords internal
generar_template_single_choice <- function(datos) {
  
  # Header YAML
  header <- c(
    "```{r data generation, echo = FALSE, results = \"hide\"}",
    "# Datos generados por ICFESMathExams",
    paste0("# Tipo: ", datos$tipo),
    paste0("# Generado: ", Sys.time()),
    "",
    "# Parámetros del ejercicio"
  )
  
  # Agregar parámetros específicos
  if (!is.null(datos$parametros_usados)) {
    for (param in names(datos$parametros_usados)) {
      header <- c(header, paste0(param, " <- ", datos$parametros_usados[[param]]))
    }
  }
  
  # Agregar datos específicos del ejercicio
  if (!is.null(datos$valores)) {
    for (valor in names(datos$valores)) {
      if (is.numeric(datos$valores[[valor]])) {
        header <- c(header, paste0(valor, " <- ", datos$valores[[valor]]))
      } else {
        header <- c(header, paste0(valor, " <- \"", datos$valores[[valor]], "\""))
      }
    }
  }
  
  header <- c(header, "```", "")
  
  # Pregunta
  pregunta <- c(
    "Question",
    "========",
    "",
    datos$enunciado %||% "Enunciado del ejercicio",
    ""
  )
  
  # Opciones de respuesta
  if (!is.null(datos$opciones)) {
    opciones <- c("Answerlist", "----------")
    for (i in seq_along(datos$opciones)) {
      opciones <- c(opciones, paste0("* ", datos$opciones[[i]]))
    }
    opciones <- c(opciones, "")
  } else {
    opciones <- c(
      "Answerlist",
      "----------",
      "* Opción A",
      "* Opción B", 
      "* Opción C",
      "* Opción D",
      ""
    )
  }
  
  # Solución
  solucion <- c(
    "Solution",
    "========",
    "",
    datos$solucion %||% "Explicación de la solución",
    ""
  )
  
  # Meta-información
  meta <- c(
    "Meta-information",
    "================",
    "extype: schoice",
    "exsolution: 1000",  # Por defecto primera opción correcta
    paste0("exname: ", datos$tipo %||% "ejercicio_icfes"),
    paste0("extol: 0.01")
  )
  
  # Combinar todo
  return(c(header, pregunta, opciones, solucion, meta))
}

#' Generar template numérico
#' 
#' @param datos Lista con datos del ejercicio
#' @return Vector de caracteres con contenido del template
#' @keywords internal
generar_template_numeric <- function(datos) {
  
  # Similar estructura pero para respuesta numérica
  header <- c(
    "```{r data generation, echo = FALSE, results = \"hide\"}",
    "# Datos generados por ICFESMathExams",
    paste0("# Tipo: ", datos$tipo),
    "",
    "# Respuesta correcta"
  )
  
  if (!is.null(datos$respuesta_correcta)) {
    header <- c(header, paste0("respuesta <- ", datos$respuesta_correcta))
  }
  
  header <- c(header, "```", "")
  
  pregunta <- c(
    "Question", 
    "========",
    "",
    datos$enunciado %||% "Enunciado del ejercicio numérico",
    ""
  )
  
  solucion <- c(
    "Solution",
    "========", 
    "",
    datos$solucion %||% "Explicación del cálculo",
    ""
  )
  
  meta <- c(
    "Meta-information",
    "================",
    "extype: num",
    paste0("exsolution: ", datos$respuesta_correcta %||% "0"),
    paste0("exname: ", datos$tipo %||% "ejercicio_numerico"),
    "extol: 0.01"
  )
  
  return(c(header, pregunta, solucion, meta))
}

#' Exportar ejercicios a formato r-exams
#' 
#' Exporta ejercicios ICFESMathExams a múltiples formatos usando r-exams
#' 
#' @param ejercicios Lista de ejercicios generados por ICFESMathExams
#' @param formatos Vector de formatos de salida ("pdf", "html", "moodle", "blackboard")
#' @param directorio_salida Directorio para archivos de salida
#' @param nombre_examen Nombre base para los archivos generados
#' @param num_versiones Número de versiones a generar
#' @return Lista con rutas de archivos generados
#' @export
#' @examples
#' \dontrun{
#' ejercicios <- ejecutar_generacion_datos(c("movimiento_lineal"), 5)
#' archivos <- exportar_rexams(ejercicios, c("pdf", "html"), "output/")
#' }
exportar_rexams <- function(ejercicios, formatos = c("pdf", "html"), 
                           directorio_salida = "output/", 
                           nombre_examen = "examen_icfes",
                           num_versiones = 1) {
  
  # Validar que r-exams esté disponible
  if (!requireNamespace("exams", quietly = TRUE)) {
    stop("El paquete 'exams' es requerido para esta función")
  }
  
  # Crear directorio temporal para templates
  temp_dir <- tempdir()
  templates_dir <- file.path(temp_dir, "templates")
  dir.create(templates_dir, showWarnings = FALSE)
  
  # Crear templates para cada ejercicio
  template_files <- c()
  for (i in seq_along(ejercicios)) {
    template_path <- crear_template_rexams(
      ejercicios[[i]], 
      "single_choice",
      templates_dir,
      paste0("ejercicio_", i)
    )
    template_files <- c(template_files, basename(template_path))
  }
  
  # Crear directorio de salida
  if (!dir.exists(directorio_salida)) {
    dir.create(directorio_salida, recursive = TRUE)
  }
  
  # Exportar a cada formato
  archivos_generados <- list()
  
  for (formato in formatos) {
    tryCatch({
      archivos_formato <- exportar_formato_especifico(
        template_files, formato, templates_dir, 
        directorio_salida, nombre_examen, num_versiones
      )
      archivos_generados[[formato]] <- archivos_formato
    }, error = function(e) {
      warning("Error exportando formato ", formato, ": ", e$message)
      archivos_generados[[formato]] <- NULL
    })
  }
  
  return(archivos_generados)
}

#' Exportar a formato específico
#' 
#' @param template_files Vector con nombres de archivos template
#' @param formato Formato de salida
#' @param templates_dir Directorio de templates
#' @param directorio_salida Directorio de salida
#' @param nombre_examen Nombre base del examen
#' @param num_versiones Número de versiones
#' @return Vector con rutas de archivos generados
#' @keywords internal
exportar_formato_especifico <- function(template_files, formato, templates_dir,
                                       directorio_salida, nombre_examen, num_versiones) {
  
  # Cambiar directorio de trabajo temporalmente
  old_wd <- getwd()
  on.exit(setwd(old_wd))
  setwd(templates_dir)
  
  archivos <- switch(formato,
    "pdf" = {
      exams::exams2pdf(
        template_files,
        n = num_versiones,
        dir = directorio_salida,
        name = nombre_examen,
        template = "plain"
      )
    },
    "html" = {
      exams::exams2html(
        template_files,
        n = num_versiones,
        dir = directorio_salida,
        name = nombre_examen
      )
    },
    "moodle" = {
      exams::exams2moodle(
        template_files,
        n = num_versiones,
        dir = directorio_salida,
        name = nombre_examen
      )
    },
    "blackboard" = {
      exams::exams2blackboard(
        template_files,
        n = num_versiones,
        dir = directorio_salida,
        name = nombre_examen
      )
    },
    stop("Formato no soportado: ", formato)
  )
  
  return(archivos)
}

#' Generar template de opción múltiple
#'
#' @param datos Lista con datos del ejercicio
#' @return Vector de caracteres con contenido del template
#' @keywords internal
generar_template_multiple_choice <- function(datos) {
  # Similar a single_choice pero permite múltiples respuestas correctas
  template <- generar_template_single_choice(datos)

  # Modificar meta-información para múltiple choice
  meta_idx <- which(grepl("extype:", template))
  if (length(meta_idx) > 0) {
    template[meta_idx] <- "extype: mchoice"

    # Ajustar solución para múltiples opciones
    sol_idx <- which(grepl("exsolution:", template))
    if (length(sol_idx) > 0) {
      # Por defecto: primera y tercera opciones correctas
      template[sol_idx] <- "exsolution: 1010"
    }
  }

  return(template)
}

#' Generar template de respuesta de texto
#'
#' @param datos Lista con datos del ejercicio
#' @return Vector de caracteres con contenido del template
#' @keywords internal
generar_template_string <- function(datos) {

  header <- c(
    "```{r data generation, echo = FALSE, results = \"hide\"}",
    "# Datos generados por ICFESMathExams",
    paste0("# Tipo: ", datos$tipo),
    "",
    "# Respuesta correcta (texto)"
  )

  if (!is.null(datos$respuesta_correcta)) {
    header <- c(header, paste0("respuesta <- \"", datos$respuesta_correcta, "\""))
  }

  header <- c(header, "```", "")

  pregunta <- c(
    "Question",
    "========",
    "",
    datos$enunciado %||% "Enunciado del ejercicio de texto",
    ""
  )

  solucion <- c(
    "Solution",
    "========",
    "",
    datos$solucion %||% "Explicación de la respuesta",
    ""
  )

  meta <- c(
    "Meta-information",
    "================",
    "extype: string",
    paste0("exsolution: ", datos$respuesta_correcta %||% "respuesta"),
    paste0("exname: ", datos$tipo %||% "ejercicio_texto")
  )

  return(c(header, pregunta, solucion, meta))
}

#' Crear examen completo r-exams desde ICFESMathExams
#'
#' Genera un examen completo usando múltiples ejercicios ICFESMathExams
#'
#' @param ejercicios Lista de ejercicios generados
#' @param configuracion_examen Lista con configuración del examen
#' @param directorio_templates Directorio para templates temporales
#' @return Lista con información del examen creado
#' @export
#' @examples
#' \dontrun{
#' ejercicios <- ejecutar_generacion_datos(c("movimiento_lineal", "estadistica_descriptiva"), 10)
#' examen <- crear_examen_completo_rexams(ejercicios)
#' }
crear_examen_completo_rexams <- function(ejercicios,
                                        configuracion_examen = list(),
                                        directorio_templates = "templates/") {

  # Configuración por defecto
  config_default <- list(
    titulo = "Examen ICFES Matemáticas",
    instrucciones = "Seleccione la respuesta correcta para cada pregunta.",
    tiempo_limite = 120, # minutos
    num_versiones = 3,
    formatos_salida = c("pdf", "html"),
    incluir_solucionario = TRUE
  )

  config <- modifyList(config_default, configuracion_examen)

  # Crear directorio de templates
  if (!dir.exists(directorio_templates)) {
    dir.create(directorio_templates, recursive = TRUE)
  }

  # Generar templates para cada ejercicio
  templates_creados <- c()
  for (i in seq_along(ejercicios)) {
    # Determinar tipo de template basado en el ejercicio
    tipo_template <- determinar_tipo_template(ejercicios[[i]])

    template_path <- crear_template_rexams(
      ejercicios[[i]],
      tipo_template,
      directorio_templates,
      paste0("ejercicio_", sprintf("%02d", i))
    )

    templates_creados <- c(templates_creados, basename(template_path))
  }

  # Crear archivo de configuración del examen
  config_examen <- crear_configuracion_examen_rexams(config, templates_creados)
  config_path <- file.path(directorio_templates, "examen_config.R")
  writeLines(config_examen, config_path)

  # Retornar información del examen
  return(list(
    templates = templates_creados,
    configuracion = config,
    directorio = directorio_templates,
    num_ejercicios = length(ejercicios),
    tipos_ejercicios = sapply(ejercicios, function(x) x$tipo %||% "desconocido"),
    timestamp = Sys.time()
  ))
}

#' Determinar tipo de template apropiado
#'
#' @param ejercicio Datos del ejercicio
#' @return Tipo de template recomendado
#' @keywords internal
determinar_tipo_template <- function(ejercicio) {

  # Lógica para determinar el mejor tipo de template
  if (!is.null(ejercicio$opciones) && length(ejercicio$opciones) > 1) {
    return("single_choice")
  } else if (!is.null(ejercicio$respuesta_correcta) && is.numeric(ejercicio$respuesta_correcta)) {
    return("numeric")
  } else if (!is.null(ejercicio$respuesta_correcta) && is.character(ejercicio$respuesta_correcta)) {
    return("string")
  } else {
    return("single_choice") # Por defecto
  }
}

#' Crear configuración de examen r-exams
#'
#' @param config Lista de configuración
#' @param templates Vector de nombres de templates
#' @return Vector de caracteres con código R de configuración
#' @keywords internal
crear_configuracion_examen_rexams <- function(config, templates) {

  config_lines <- c(
    "# Configuración de Examen ICFESMathExams",
    paste0("# Generado: ", Sys.time()),
    "",
    "# Configuración básica",
    paste0("titulo_examen <- \"", config$titulo, "\""),
    paste0("instrucciones <- \"", config$instrucciones, "\""),
    paste0("tiempo_limite <- ", config$tiempo_limite),
    paste0("num_versiones <- ", config$num_versiones),
    "",
    "# Templates de ejercicios",
    paste0("templates <- c(", paste0("\"", templates, "\"", collapse = ", "), ")"),
    "",
    "# Función para generar examen",
    "generar_examen <- function() {",
    "  exams::exams2pdf(",
    "    templates,",
    "    n = num_versiones,",
    "    name = 'examen_icfes',",
    "    dir = 'output/',",
    "    template = 'plain'",
    "  )",
    "}"
  )

  return(config_lines)
}

#' Validar compatibilidad con r-exams
#'
#' Verifica que los ejercicios ICFESMathExams sean compatibles con r-exams
#'
#' @param ejercicios Lista de ejercicios a validar
#' @return Lista con resultados de validación
#' @export
validar_compatibilidad_rexams <- function(ejercicios) {

  resultados <- list(
    compatible = TRUE,
    errores = c(),
    advertencias = c(),
    ejercicios_validados = 0,
    ejercicios_totales = length(ejercicios)
  )

  for (i in seq_along(ejercicios)) {
    ejercicio <- ejercicios[[i]]

    # Validaciones básicas
    if (is.null(ejercicio$enunciado) || nchar(ejercicio$enunciado) == 0) {
      resultados$errores <- c(resultados$errores,
                             paste("Ejercicio", i, ": Falta enunciado"))
      resultados$compatible <- FALSE
    }

    if (is.null(ejercicio$tipo)) {
      resultados$advertencias <- c(resultados$advertencias,
                                  paste("Ejercicio", i, ": Tipo no especificado"))
    }

    # Validar respuesta correcta
    if (is.null(ejercicio$respuesta_correcta)) {
      resultados$errores <- c(resultados$errores,
                             paste("Ejercicio", i, ": Falta respuesta correcta"))
      resultados$compatible <- FALSE
    }

    resultados$ejercicios_validados <- resultados$ejercicios_validados + 1
  }

  return(resultados)
}

#' Operador %||% para valores por defecto
#' @keywords internal
`%||%` <- function(x, y) if (is.null(x)) y else x
