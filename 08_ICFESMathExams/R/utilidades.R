#' @title Funciones de Utilidad General para ICFESMathExams
#' @description Este archivo contiene funciones de utilidad general utilizadas
#' a lo largo del paquete ICFESMathExams.

#' Generar metadatos del sistema
#' 
#' Genera metadatos completos del sistema para trazabilidad y debugging.
#' 
#' @return Lista con información del sistema, versiones de paquetes y configuración
#' @export
#' @examples
#' metadatos <- generar_metadatos_sistema()
#' print(metadatos$r_version)
generar_metadatos_sistema <- function() {
  list(
    timestamp = Sys.time(),
    r_version = R.version.string,
    platform = R.version$platform,
    paquete_version = utils::packageVersion("ICFESMathExams"),
    dependencias = sapply(c("exams", "digest", "ggplot2"), utils::packageVersion),
    locale = Sys.getlocale(),
    wd = getwd(),
    usuario = Sys.info()[["user"]],
    os = Sys.info()[["sysname"]]
  )
}

#' Obtener información del paquete
#' 
#' Proporciona información resumida sobre el paquete ICFESMathExams.
#' 
#' @return Lista con información del paquete
#' @export
#' @examples
#' info <- info_paquete()
#' cat("Versión:", info$version)
info_paquete <- function() {
  desc <- utils::packageDescription("ICFESMathExams")
  list(
    nombre = desc$Package,
    version = desc$Version,
    titulo = desc$Title,
    autor = desc$Author,
    descripcion = desc$Description,
    url = desc$URL,
    licencia = desc$License
  )
}

#' Detectar tipo de ejercicio
#' 
#' Detecta automáticamente el tipo de ejercicio basado en parámetros o contenido.
#' 
#' @param datos Lista con datos del ejercicio
#' @return Cadena con el tipo de ejercicio detectado
#' @export
#' @examples
#' datos <- list(velocidad = 30, tiempo = 5)
#' tipo <- detectar_tipo_ejercicio(datos)
detectar_tipo_ejercicio <- function(datos) {
  if (is.null(datos) || length(datos) == 0) {
    return("desconocido")
  }
  
  # Detectar por nombres de variables
  nombres <- names(datos)
  
  if (any(c("velocidad", "tiempo", "distancia") %in% nombres)) {
    return("movimiento_lineal")
  } else if (any(c("media", "desviacion", "muestra") %in% nombres)) {
    return("estadistica_descriptiva") 
  } else if (any(c("probabilidad", "evento") %in% nombres)) {
    return("probabilidad_basica")
  } else if (any(c("exponencial", "base", "exponente") %in% nombres)) {
    return("variacion_exponencial")
  } else {
    return("generico")
  }
}

#' Validar parámetros de entrada
#' 
#' Valida que los parámetros de entrada cumplan los requisitos mínimos.
#' 
#' @param parametros Lista de parámetros a validar
#' @param tipo Tipo de ejercicio para validaciones específicas
#' @return Lista con resultado de validación
#' @export
#' @examples
#' params <- list(velocidad_min = 10, velocidad_max = 50)
#' resultado <- validar_parametros_entrada(params, "movimiento_lineal")
validar_parametros_entrada <- function(parametros, tipo = "generico") {
  errores <- c()
  advertencias <- c()
  
  # Validaciones generales
  if (is.null(parametros) || length(parametros) == 0) {
    errores <- c(errores, "Parámetros no pueden estar vacíos")
  }
  
  # Validaciones específicas por tipo
  if (tipo == "movimiento_lineal") {
    if (!is.null(parametros$velocidad_min) && !is.null(parametros$velocidad_max)) {
      if (parametros$velocidad_min >= parametros$velocidad_max) {
        errores <- c(errores, "velocidad_min debe ser menor que velocidad_max")
      }
    }
  }
  
  list(
    valido = length(errores) == 0,
    errores = errores,
    advertencias = advertencias,
    timestamp = Sys.time()
  )
}

#' Crear hash de versión
#' 
#' Crea un hash único para identificar versiones específicas de ejercicios.
#' 
#' @param datos Datos del ejercicio
#' @return Cadena con hash MD5
#' @export
#' @examples
#' datos <- list(tipo = "movimiento_lineal", velocidad = 30)
#' hash <- crear_hash_version(datos)
crear_hash_version <- function(datos) {
  if (requireNamespace("digest", quietly = TRUE)) {
    digest::digest(datos, algo = "md5")
  } else {
    # Fallback simple si digest no está disponible
    paste0("hash_", as.integer(Sys.time()), "_", sample(1000:9999, 1))
  }
}

#' Generar timestamp formateado
#' 
#' Genera un timestamp en formato legible para logging y reportes.
#' 
#' @param formato Formato de fecha (por defecto ISO 8601)
#' @return Cadena con timestamp formateado
#' @export
#' @examples
#' timestamp <- generar_timestamp()
#' timestamp_custom <- generar_timestamp("%d/%m/%Y %H:%M")
generar_timestamp <- function(formato = "%Y-%m-%d %H:%M:%S") {
  format(Sys.time(), formato)
}

#' Validar estructura de archivo Rmd
#' 
#' Valida que un archivo R Markdown tenga la estructura correcta para exams.
#' 
#' @param ruta_archivo Ruta al archivo .Rmd
#' @return Lista con resultado de validación
#' @export
#' @examples
#' \dontrun{
#' resultado <- validar_estructura_rmd("ejercicio.Rmd")
#' }
validar_estructura_rmd <- function(ruta_archivo) {
  if (!file.exists(ruta_archivo)) {
    return(list(valido = FALSE, error = "Archivo no encontrado"))
  }
  
  contenido <- readLines(ruta_archivo, warn = FALSE)
  
  # Buscar elementos requeridos
  tiene_yaml <- any(grepl("^---", contenido))
  tiene_pregunta <- any(grepl("Question", contenido))
  tiene_solucion <- any(grepl("Solution", contenido))
  
  list(
    valido = tiene_yaml && tiene_pregunta && tiene_solucion,
    yaml_header = tiene_yaml,
    seccion_pregunta = tiene_pregunta,
    seccion_solucion = tiene_solucion,
    num_lineas = length(contenido)
  )
}

#' Generar configuración por defecto
#' 
#' Genera una configuración por defecto para diferentes tipos de ejercicios.
#' 
#' @param tipo Tipo de ejercicio
#' @return Lista con configuración por defecto
#' @export
#' @examples
#' config <- generar_configuracion_defecto("movimiento_lineal")
generar_configuracion_defecto <- function(tipo = "movimiento_lineal") {
  configs <- list(
    movimiento_lineal = list(
      velocidad_min = 10,
      velocidad_max = 100,
      tiempo_min = 1,
      tiempo_max = 10,
      unidades_velocidad = "m/s",
      unidades_tiempo = "s",
      decimales = 2
    ),
    estadistica_descriptiva = list(
      tamaño_muestra_min = 20,
      tamaño_muestra_max = 100,
      media_min = 50,
      media_max = 150,
      desviacion_min = 5,
      desviacion_max = 25,
      decimales = 1
    ),
    probabilidad_basica = list(
      eventos_min = 2,
      eventos_max = 6,
      probabilidad_min = 0.1,
      probabilidad_max = 0.9,
      decimales = 3
    ),
    variacion_exponencial = list(
      base_min = 2,
      base_max = 10,
      exponente_min = 1,
      exponente_max = 5,
      decimales = 2
    )
  )
  
  configs[[tipo]] %||% configs$movimiento_lineal
}

#' Formatear resultados para presentación
#' 
#' Formatea resultados numéricos para presentación en ejercicios.
#' 
#' @param valor Valor numérico a formatear
#' @param decimales Número de decimales
#' @param unidades Unidades a añadir
#' @return Cadena formateada
#' @export
#' @examples
#' resultado <- formatear_resultados(15.678, 2, "m/s")
formatear_resultados <- function(valor, decimales = 2, unidades = "") {
  valor_formateado <- round(valor, decimales)
  if (unidades != "") {
    paste(valor_formateado, unidades)
  } else {
    as.character(valor_formateado)
  }
}

# Operador auxiliar para valores por defecto
`%||%` <- function(x, y) if (is.null(x)) y else x