#' @title Inicialización y Configuración del Paquete ICFESMathExams
#' @description Funciones de inicialización que se ejecutan al cargar el paquete

#' Función ejecutada al cargar el paquete
#' 
#' Se ejecuta automáticamente cuando se carga el paquete con library()
#' 
#' @param libname Nombre de la librería
#' @param pkgname Nombre del paquete
.onAttach <- function(libname, pkgname) {
  # Mensaje de bienvenida
  packageStartupMessage(
    "\n", 
    "═══════════════════════════════════════════════════════════════════\n",
    "📚 ICFESMathExams v", utils::packageVersion("ICFESMathExams"), " - Generación de Exámenes ICFES\n",
    "═══════════════════════════════════════════════════════════════════\n",
    "✅ Paquete cargado exitosamente\n",
    "🎯 Para empezar: ?generar_datos o vignette('introduccion')\n",
    "📖 Documentación: browseVignettes('ICFESMathExams')\n",
    "🚀 Funciones principales:\n",
    "   • generar_datos() - Generar ejercicio individual\n",
    "   • ejecutar_generacion_datos() - Generación masiva\n",
    "   • validar_datos_icfes() - Validar calidad\n",
    "   • generar_reporte_calidad() - Análisis completo\n",
    "═══════════════════════════════════════════════════════════════════"
  )
  
  # Verificar dependencias críticas de forma silenciosa
  invisible(verificar_dependencias())
  
  # Configurar entorno si es necesario
  invisible(configurar_entorno())
}

#' Función ejecutada al cargar el namespace
#' 
#' Se ejecuta automáticamente cuando se carga el namespace del paquete
#' 
#' @param libname Nombre de la librería
#' @param pkgname Nombre del paquete
.onLoad <- function(libname, pkgname) {
  # Configurar opciones específicas del paquete
  op <- options()
  op_icfes <- list(
    ICFESMathExams.verbose = TRUE,
    ICFESMathExams.seed_default = NULL,
    ICFESMathExams.validacion_estricta = TRUE,
    ICFESMathExams.contexto_default = "colombiano"
  )
  
  # Solo establecer opciones que no existen
  toset <- !(names(op_icfes) %in% names(op))
  if (any(toset)) options(op_icfes[toset])
  
  # Registrar el paquete internamente
  invisible()
}

#' Verificar dependencias críticas
#' 
#' Verifica que las dependencias críticas estén disponibles y funcionando
#' 
#' @return Lista con estado de las dependencias
#' @export
verificar_dependencias <- function() {
  dependencias_criticas <- c("digest", "ggplot2", "dplyr")
  dependencias_opcionales <- c("reticulate", "futile.logger", "memoise")
  
  estado <- list()
  advertencias <- c()
  
  # Verificar dependencias críticas
  for (dep in dependencias_criticas) {
    disponible <- requireNamespace(dep, quietly = TRUE)
    estado[[dep]] <- list(
      disponible = disponible,
      critica = TRUE
    )
    
    if (!disponible) {
      advertencias <- c(advertencias, 
        paste("Dependencia crítica faltante:", dep, "- Instalar con install.packages('", dep, "')")
      )
    }
  }
  
  # Verificar dependencias opcionales
  for (dep in dependencias_opcionales) {
    disponible <- requireNamespace(dep, quietly = TRUE)
    estado[[dep]] <- list(
      disponible = disponible,
      critica = FALSE
    )
  }
  
  # Verificar LaTeX (opcional)
  latex_disponible <- system("which pdflatex", ignore.stdout = TRUE, ignore.stderr = TRUE) == 0
  estado[["latex"]] <- list(
    disponible = latex_disponible,
    critica = FALSE
  )
  
  # Mostrar advertencias solo si hay dependencias críticas faltantes
  if (length(advertencias) > 0) {
    for (adv in advertencias) {
      packageStartupMessage("⚠️ ", adv)
    }
  }
  
  # Mensaje sobre dependencias opcionales
  opcionales_faltantes <- dependencias_opcionales[!sapply(dependencias_opcionales, requireNamespace, quietly = TRUE)]
  if (length(opcionales_faltantes) > 0) {
    packageStartupMessage("💡 Dependencias opcionales no disponibles: ", 
                         paste(opcionales_faltantes, collapse = ", "),
                         "\n   Algunas funciones avanzadas pueden no estar disponibles")
  }
  
  return(invisible(estado))
}

#' Configurar entorno del paquete
#' 
#' Configura el entorno necesario para el funcionamiento óptimo del paquete
#' 
#' @return Lista con configuración aplicada
#' @export
configurar_entorno <- function() {
  configuracion <- list()
  
  # Configurar encoding para caracteres especiales
  if (Sys.info()[["sysname"]] == "Windows") {
    if (l10n_info()$`UTF-8`) {
      Sys.setlocale("LC_CTYPE", "Spanish_Colombia.UTF-8")
    }
  }
  
  # Configurar random seed por defecto si no está establecida
  if (is.null(getOption("ICFESMathExams.seed_default"))) {
    # No establecer seed por defecto para mantener aleatoriedad
    configuracion$seed <- "no_establecida"
  } else {
    configuracion$seed <- getOption("ICFESMathExams.seed_default")
  }
  
  # Configurar logging si futile.logger está disponible
  if (requireNamespace("futile.logger", quietly = TRUE)) {
    # Configurar nivel de log
    futile.logger::flog.threshold(futile.logger::INFO)
    configuracion$logging <- "habilitado"
  } else {
    configuracion$logging <- "no_disponible"
  }
  
  # Configurar procesamiento paralelo si está disponible
  if (requireNamespace("future", quietly = TRUE)) {
    # Usar plan por defecto
    configuracion$paralelo <- "disponible"
  } else {
    configuracion$paralelo <- "no_disponible"
  }
  
  # Configurar reticulate para Python si está disponible
  if (requireNamespace("reticulate", quietly = TRUE)) {
    tryCatch({
      # Intentar configurar Python
      reticulate::use_python("/usr/bin/python3", required = FALSE)
      configuracion$python <- "configurado"
    }, error = function(e) {
      configuracion$python <- "no_configurado"
    })
  } else {
    configuracion$python <- "no_disponible"
  }
  
  return(invisible(configuracion))
}

#' Obtener configuración actual del paquete
#' 
#' Devuelve la configuración actual y estado del paquete
#' 
#' @return Lista con toda la configuración
#' @export
#' @examples
#' config <- obtener_configuracion()
#' print(config$dependencias)
obtener_configuracion <- function() {
  list(
    version_paquete = utils::packageVersion("ICFESMathExams"),
    version_r = R.version.string,
    opciones_paquete = list(
      verbose = getOption("ICFESMathExams.verbose", TRUE),
      seed_default = getOption("ICFESMathExams.seed_default", NULL),
      validacion_estricta = getOption("ICFESMathExams.validacion_estricta", TRUE),
      contexto_default = getOption("ICFESMathExams.contexto_default", "colombiano")
    ),
    dependencias = verificar_dependencias(),
    entorno = configurar_entorno(),
    sistema = list(
      os = Sys.info()[["sysname"]],
      usuario = Sys.info()[["user"]],
      locale = Sys.getlocale(),
      timezone = Sys.timezone(),
      working_directory = getwd()
    ),
    estadisticas_sesion = list(
      timestamp_carga = Sys.time(),
      objetos_en_workspace = length(ls(envir = .GlobalEnv)),
      memoria_usada = if(requireNamespace("utils", quietly = TRUE)) {
        format(utils::object.size(ls(envir = .GlobalEnv)), units = "MB")
      } else {
        "no_disponible"
      }
    )
  )
}

# Función auxiliar para limpiar al descargar el paquete
.onUnload <- function(libpath) {
  # Limpiar opciones específicas del paquete
  options(list(
    ICFESMathExams.verbose = NULL,
    ICFESMathExams.seed_default = NULL,
    ICFESMathExams.validacion_estricta = NULL,
    ICFESMathExams.contexto_default = NULL
  ))
  
  # Mensaje de despedida
  message("ICFESMathExams descargado. ¡Gracias por usar el paquete!")
}