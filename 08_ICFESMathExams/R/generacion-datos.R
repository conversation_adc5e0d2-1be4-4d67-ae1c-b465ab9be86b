#' @title Funciones de Generación de Datos para Ejercicios ICFES
#' @description Este archivo contiene las funciones principales para generar datos
#' aleatorios para diferentes tipos de ejercicios de matemáticas ICFES.

#' Ejecutar generación de datos masiva
#' 
#' Función principal para generar múltiples versiones de ejercicios de forma masiva.
#' 
#' @param tipos_ejercicios Vector con tipos de ejercicios a generar
#' @param num_versiones Número de versiones únicas a generar
#' @param seed Semilla para reproducibilidad (opcional)
#' @param configuracion Lista de configuración adicional
#' @param validar_cada Validar calidad cada N ejercicios
#' @return Lista con ejercicios generados
#' @export
#' @examples
#' versiones <- ejecutar_generacion_datos(
#'   tipos_ejercicios = c("movimiento_lineal", "estadistica_descriptiva"),
#'   num_versiones = 10,
#'   seed = 123
#' )
ejecutar_generacion_datos <- function(tipos_ejercicios, num_versiones = 1, 
                                    seed = NULL, configuracion = list(),
                                    validar_cada = 50) {
  
  # Configurar semilla si se proporciona
  if (!is.null(seed)) {
    set.seed(seed)
  }
  
  # Validar parámetros de entrada
  if (length(tipos_ejercicios) == 0) {
    stop("Debe especificar al menos un tipo de ejercicio")
  }
  
  if (num_versiones <= 0) {
    stop("num_versiones debe ser mayor que 0")
  }
  
  # Inicializar contenedores
  ejercicios_generados <- list()
  tipos_disponibles <- c("movimiento_lineal", "estadistica_descriptiva", 
                        "probabilidad_basica", "variacion_exponencial")
  
  # Validar tipos de ejercicios
  tipos_invalidos <- setdiff(tipos_ejercicios, tipos_disponibles)
  if (length(tipos_invalidos) > 0) {
    warning("Tipos de ejercicios no válidos: ", paste(tipos_invalidos, collapse = ", "))
    tipos_ejercicios <- intersect(tipos_ejercicios, tipos_disponibles)
  }
  
  # Generar ejercicios
  for (i in seq_len(num_versiones)) {
    # Seleccionar tipo de ejercicio (rotar si hay múltiples)
    tipo_actual <- tipos_ejercicios[((i - 1) %% length(tipos_ejercicios)) + 1]
    
    # Generar ejercicio individual
    ejercicio <- generar_datos(
      tipo = tipo_actual,
      parametros = configuracion
    )
    
    # Añadir metadatos
    ejercicio$version_id <- i
    ejercicio$timestamp <- Sys.time()
    ejercicio$hash <- crear_hash_version(ejercicio)
    
    ejercicios_generados[[i]] <- ejercicio
    
    # Validación periódica
    if (i %% validar_cada == 0) {
      message("Generados ", i, " de ", num_versiones, " ejercicios...")
    }
  }
  
  message("Generación completa: ", length(ejercicios_generados), " ejercicios únicos")
  return(ejercicios_generados)
}

#' Generar datos para un ejercicio específico
#' 
#' Función principal para generar datos aleatorios para un tipo específico de ejercicio.
#' 
#' @param tipo Tipo de ejercicio ("movimiento_lineal", "estadistica_descriptiva", etc.)
#' @param parametros Lista de parámetros específicos (opcional)
#' @return Lista con datos del ejercicio generado
#' @export
#' @examples
#' datos <- generar_datos(
#'   tipo = "movimiento_lineal",
#'   parametros = list(velocidad_min = 20, velocidad_max = 60)
#' )
generar_datos <- function(tipo = "movimiento_lineal", parametros = list()) {
  
  # Validar tipo
  tipos_validos <- c("movimiento_lineal", "estadistica_descriptiva", 
                    "probabilidad_basica", "variacion_exponencial")
  
  if (!tipo %in% tipos_validos) {
    stop("Tipo de ejercicio no válido. Tipos disponibles: ", 
         paste(tipos_validos, collapse = ", "))
  }
  
  # Generar según el tipo
  resultado <- switch(tipo,
    "movimiento_lineal" = generar_movimiento_lineal(parametros),
    "estadistica_descriptiva" = generar_estadistica_descriptiva(parametros),
    "probabilidad_basica" = generar_probabilidad_basica(parametros),
    "variacion_exponencial" = generar_variacion_exponencial(parametros),
    stop("Tipo no implementado: ", tipo)
  )
  
  # Añadir metadatos comunes
  resultado$tipo <- tipo
  resultado$parametros_usados <- parametros
  resultado$timestamp_generacion <- Sys.time()
  resultado$version_hash <- crear_hash_version(resultado)
  
  return(resultado)
}

#' Generar ejercicio de movimiento lineal
#' 
#' Genera datos aleatorios para ejercicios de movimiento rectilíneo uniforme.
#' 
#' @param parametros Lista de parámetros (velocidad_min, velocidad_max, etc.)
#' @return Lista con datos del ejercicio
#' @export
#' @examples
#' ejercicio <- generar_movimiento_lineal(list(velocidad_min = 30, velocidad_max = 80))
generar_movimiento_lineal <- function(parametros = list()) {
  
  # Configuración por defecto
  config <- generar_configuracion_defecto("movimiento_lineal")
  
  # Combinar con parámetros proporcionados
  config <- modifyList(config, parametros)
  
  # Generar valores aleatorios
  velocidad <- runif(1, config$velocidad_min, config$velocidad_max)
  tiempo <- runif(1, config$tiempo_min, config$tiempo_max)
  distancia <- velocidad * tiempo
  
  # Generar distractores
  distractores <- generar_distractores_movimiento(velocidad, tiempo, distancia)
  
  # Crear enunciado
  enunciado <- sprintf(
    "Un objeto se mueve con velocidad constante de %.1f %s durante %.1f %s. ¿Cuál es la distancia recorrida?",
    velocidad, config$unidades_velocidad, tiempo, config$unidades_tiempo
  )
  
  list(
    enunciado = enunciado,
    velocidad = round(velocidad, config$decimales),
    tiempo = round(tiempo, config$decimales),
    distancia = round(distancia, config$decimales),
    respuesta_correcta = round(distancia, config$decimales),
    distractores = distractores,
    unidades_respuesta = paste(config$unidades_velocidad, "*", config$unidades_tiempo),
    dificultad = calcular_dificultad_movimiento(velocidad, tiempo),
    contexto = "Un automóvil viaja por una carretera colombiana"
  )
}

#' Generar distractores para movimiento lineal
#' 
#' Genera respuestas incorrectas plausibles para ejercicios de movimiento.
#' 
#' @param velocidad Velocidad del objeto
#' @param tiempo Tiempo de movimiento  
#' @param distancia Distancia correcta
#' @return Vector con distractores
#' @export
generar_distractores_movimiento <- function(velocidad, tiempo, distancia) {
  c(
    round(velocidad + tiempo, 2),        # Error conceptual: suma en lugar de producto
    round(velocidad / tiempo, 2),        # Error: división en lugar de multiplicación
    round(distancia * 0.5, 2),          # Error de cálculo: mitad del resultado
    round(distancia * 2, 2)             # Error de cálculo: doble del resultado
  )
}

#' Generar ejercicio de variación exponencial
#' 
#' Genera datos aleatorios para ejercicios de funciones exponenciales.
#' 
#' @param parametros Lista de parámetros (base_min, base_max, etc.)
#' @return Lista con datos del ejercicio
#' @export
generar_variacion_exponencial <- function(parametros = list()) {
  
  config <- generar_configuracion_defecto("variacion_exponencial")
  config <- modifyList(config, parametros)
  
  base <- sample(config$base_min:config$base_max, 1)
  exponente <- sample(config$exponente_min:config$exponente_max, 1)
  resultado <- base^exponente
  
  distractores <- generar_distractores_exponencial(base, exponente, resultado)
  
  enunciado <- sprintf(
    "Una población de bacterias se duplica cada hora. Si inicialmente hay %d bacterias, ¿cuántas habrá después de %d horas?",
    base, exponente
  )
  
  list(
    enunciado = enunciado,
    base = base,
    exponente = exponente,
    resultado = resultado,
    respuesta_correcta = resultado,
    distractores = distractores,
    formula = paste0(base, "^", exponente),
    contexto = "Crecimiento poblacional en laboratorio colombiano"
  )
}

#' Generar distractores para variación exponencial
#' 
#' @param base Base de la exponencial
#' @param exponente Exponente
#' @param resultado Resultado correcto
#' @return Vector con distractores
#' @export
generar_distractores_exponencial <- function(base, exponente, resultado) {
  c(
    base * exponente,                    # Error: multiplicación en lugar de potencia
    base + exponente,                    # Error: suma en lugar de potencia
    round(resultado * 0.5),              # Error de cálculo
    round(resultado * 1.5)               # Error de cálculo
  )
}

#' Generar ejercicio de estadística descriptiva
#' 
#' Genera datos aleatorios para ejercicios de medidas de tendencia central.
#' 
#' @param parametros Lista de parámetros (tamaño_muestra, etc.)
#' @return Lista con datos del ejercicio
#' @export
generar_estadistica_descriptiva <- function(parametros = list()) {
  
  config <- generar_configuracion_defecto("estadistica_descriptiva")
  config <- modifyList(config, parametros)
  
  # Generar muestra aleatoria
  tamaño <- sample(config$tamaño_muestra_min:config$tamaño_muestra_max, 1)
  media_poblacion <- runif(1, config$media_min, config$media_max)
  desviacion <- runif(1, config$desviacion_min, config$desviacion_max)
  
  muestra <- round(rnorm(tamaño, media_poblacion, desviacion), config$decimales)
  
  # Calcular estadísticas
  media_muestra <- mean(muestra)
  mediana_muestra <- median(muestra)
  moda_muestra <- as.numeric(names(sort(table(muestra), decreasing = TRUE))[1])
  
  enunciado <- sprintf(
    "En un estudio sobre las calificaciones de %d estudiantes colombianos en matemáticas, se obtuvieron los siguientes datos: %s. ¿Cuál es la media?",
    tamaño,
    paste(head(muestra, 10), collapse = ", ")
  )
  
  list(
    enunciado = enunciado,
    muestra = muestra,
    tamaño_muestra = tamaño,
    media = round(media_muestra, config$decimales),
    mediana = round(mediana_muestra, config$decimales),
    moda = moda_muestra,
    respuesta_correcta = round(media_muestra, config$decimales),
    contexto = "Rendimiento académico en Colombia"
  )
}

#' Generar ejercicio de probabilidad básica
#' 
#' Genera datos aleatorios para ejercicios de probabilidad.
#' 
#' @param parametros Lista de parámetros
#' @return Lista con datos del ejercicio
#' @export
generar_probabilidad_basica <- function(parametros = list()) {
  
  config <- generar_configuracion_defecto("probabilidad_basica")
  config <- modifyList(config, parametros)
  
  # Generar escenario de probabilidad
  total_eventos <- sample(config$eventos_min:config$eventos_max, 1) * 10
  eventos_favorables <- sample(1:(total_eventos-1), 1)
  probabilidad <- eventos_favorables / total_eventos
  
  enunciado <- sprintf(
    "En una urna hay %d bolas, de las cuales %d son rojas. Si se extrae una bola al azar, ¿cuál es la probabilidad de que sea roja?",
    total_eventos, eventos_favorables
  )
  
  list(
    enunciado = enunciado,
    total_eventos = total_eventos,
    eventos_favorables = eventos_favorables,
    probabilidad = round(probabilidad, config$decimales),
    respuesta_correcta = round(probabilidad, config$decimales),
    porcentaje = round(probabilidad * 100, 1),
    contexto = "Experimento de probabilidad en aula colombiana"
  )
}

#' Generar versión única de ejercicio
#' 
#' Genera una versión completamente única de un ejercicio, evitando repeticiones.
#' 
#' @param tipo Tipo de ejercicio
#' @param versiones_existentes Lista de versiones ya generadas (opcional)
#' @param max_intentos Número máximo de intentos para generar versión única
#' @return Lista con ejercicio único
#' @export
#' @examples
#' ejercicio_unico <- generar_version_unica("movimiento_lineal")
generar_version_unica <- function(tipo, versiones_existentes = list(), max_intentos = 100) {
  
  hashes_existentes <- sapply(versiones_existentes, function(x) x$version_hash %||% "")
  
  for (intento in seq_len(max_intentos)) {
    # Generar nueva versión con parámetros aleatorios
    parametros_aleatorios <- generar_parametros_aleatorios(tipo)
    nuevo_ejercicio <- generar_datos(tipo, parametros_aleatorios)
    
    # Verificar unicidad
    if (!nuevo_ejercicio$version_hash %in% hashes_existentes) {
      nuevo_ejercicio$intento_generacion <- intento
      return(nuevo_ejercicio)
    }
  }
  
  warning("No se pudo generar versión única después de ", max_intentos, " intentos")
  return(generar_datos(tipo))
}

# Función auxiliar para generar parámetros aleatorios
generar_parametros_aleatorios <- function(tipo) {
  base_config <- generar_configuracion_defecto(tipo)
  
  # Añadir variación aleatoria a los parámetros
  switch(tipo,
    "movimiento_lineal" = list(
      velocidad_min = runif(1, 5, 30),
      velocidad_max = runif(1, 50, 150),
      tiempo_min = runif(1, 0.5, 3),
      tiempo_max = runif(1, 5, 20)
    ),
    "estadistica_descriptiva" = list(
      tamaño_muestra_min = sample(15:25, 1),
      tamaño_muestra_max = sample(80:120, 1),
      media_min = runif(1, 30, 70),
      media_max = runif(1, 100, 200)
    ),
    base_config
  )
}

# Función auxiliar para calcular dificultad
calcular_dificultad_movimiento <- function(velocidad, tiempo) {
  # Clasificar dificultad basada en los valores numéricos
  if (velocidad <= 30 && tiempo <= 5) {
    "facil"
  } else if (velocidad <= 80 && tiempo <= 15) {
    "intermedio"
  } else {
    "dificil"
  }
}