#' @title Sistema de Testing Unitario para ICFESMathExams
#' @description Funciones para testing automatizado y generación de reportes de calidad

#' Ejecutar tests unitarios
#' 
#' Función principal que ejecuta todos los tests disponibles en un conjunto de datos
#' 
#' @param datos_ejercicios Lista de ejercicios a testear
#' @param tipos_tests Vector con tipos de tests a ejecutar
#' @return Lista con resultados de todos los tests
#' @export
#' @examples
#' ejercicios <- ejecutar_generacion_datos("movimiento_lineal", 10)
#' resultados <- ejecutar_tests_unitarios(ejercicios)
ejecutar_tests_unitarios <- function(datos_ejercicios, 
                                   tipos_tests = c("consistencia", "diversidad", "calidad")) {
  
  if (length(datos_ejercicios) == 0) {
    return(list(error = "No hay datos para testear"))
  }
  
  resultados <- list()
  
  # Test de consistencia matemática
  if ("consistencia" %in% tipos_tests) {
    resultados$consistencia <- testear_consistencia_matematica(datos_ejercicios)
  }
  
  # Test de diversidad de parámetros
  if ("diversidad" %in% tipos_tests) {
    resultados$diversidad <- testear_diversidad_parametros(datos_ejercicios)
  }
  
  # Test de calidad general
  if ("calidad" %in% tipos_tests) {
    resultados$calidad <- validar_lote_versiones(datos_ejercicios)
  }
  
  # Resumen general
  resultados$resumen <- list(
    total_ejercicios = length(datos_ejercicios),
    tests_ejecutados = tipos_tests,
    timestamp = Sys.time(),
    estado_general = determinar_estado_general(resultados)
  )
  
  return(resultados)
}

#' Testear consistencia matemática
#' 
#' Verifica que todos los cálculos matemáticos sean correctos y consistentes
#' 
#' @param datos_ejercicios Lista de ejercicios a verificar
#' @return Lista con resultados del test de consistencia
#' @export
testear_consistencia_matematica <- function(datos_ejercicios) {
  
  inconsistencias <- 0
  detalles_errores <- c()
  
  for (i in seq_along(datos_ejercicios)) {
    ejercicio <- datos_ejercicios[[i]]
    
    # Verificar según tipo de ejercicio
    if (!is.null(ejercicio$tipo)) {
      
      if (ejercicio$tipo == "movimiento_lineal") {
        # Verificar d = v * t
        if (!is.null(ejercicio$velocidad) && !is.null(ejercicio$tiempo)) {
          distancia_calculada <- ejercicio$velocidad * ejercicio$tiempo
          diferencia <- abs(distancia_calculada - ejercicio$respuesta_correcta)
          
          if (diferencia > 0.01) {
            inconsistencias <- inconsistencias + 1
            detalles_errores <- c(detalles_errores, 
              paste("Ejercicio", i, ": Error en cálculo de distancia"))
          }
        }
        
      } else if (ejercicio$tipo == "estadistica_descriptiva") {
        # Verificar media
        if (!is.null(ejercicio$muestra)) {
          media_calculada <- mean(ejercicio$muestra)
          diferencia <- abs(media_calculada - ejercicio$respuesta_correcta)
          
          if (diferencia > 0.1) {
            inconsistencias <- inconsistencias + 1
            detalles_errores <- c(detalles_errores,
              paste("Ejercicio", i, ": Error en cálculo de media"))
          }
        }
        
      } else if (ejercicio$tipo == "variacion_exponencial") {
        # Verificar exponencial
        if (!is.null(ejercicio$base) && !is.null(ejercicio$exponente)) {
          resultado_calculado <- ejercicio$base^ejercicio$exponente
          
          if (abs(resultado_calculado - ejercicio$respuesta_correcta) > 0.01) {
            inconsistencias <- inconsistencias + 1
            detalles_errores <- c(detalles_errores,
              paste("Ejercicio", i, ": Error en cálculo exponencial"))
          }
        }
      }
    }
  }
  
  tasa_consistencia <- 1 - (inconsistencias / length(datos_ejercicios))
  
  list(
    consistencia_matematica = tasa_consistencia,
    ejercicios_inconsistentes = inconsistencias,
    total_ejercicios = length(datos_ejercicios),
    detalles_errores = detalles_errores,
    aprobado = tasa_consistencia >= 0.95,
    recomendacion = if (tasa_consistencia >= 0.95) {
      "Consistencia matemática excelente"
    } else if (tasa_consistencia >= 0.9) {
      "Consistencia matemática buena, revisar algunos ejercicios"
    } else {
      "Problemas de consistencia matemática, revisar implementación"
    }
  )
}

#' Testear diversidad de parámetros
#' 
#' Verifica que los ejercicios generados tengan suficiente diversidad en sus parámetros
#' 
#' @param datos_ejercicios Lista de ejercicios a analizar
#' @return Lista con resultados del análisis de diversidad
#' @export
testear_diversidad_parametros <- function(datos_ejercicios) {
  
  if (length(datos_ejercicios) < 2) {
    return(list(
      error = "Se necesitan al menos 2 ejercicios para evaluar diversidad",
      diversidad = 0
    ))
  }
  
  # Analizar diversidad por tipo de ejercicio
  tipos <- sapply(datos_ejercicios, function(x) x$tipo %||% "desconocido")
  tipos_unicos <- length(unique(tipos))
  
  # Analizar diversidad de respuestas
  respuestas <- sapply(datos_ejercicios, function(x) x$respuesta_correcta %||% NA)
  respuestas <- respuestas[!is.na(respuestas)]
  respuestas_unicas <- length(unique(round(respuestas, 2)))
  
  # Analizar diversidad de hashes (unicidad)
  hashes <- sapply(datos_ejercicios, function(x) x$version_hash %||% paste0("hash_", runif(1)))
  hashes_unicos <- length(unique(hashes))
  
  # Calcular métricas de diversidad
  diversidad_tipos <- tipos_unicos / length(unique(c("movimiento_lineal", "estadistica_descriptiva", 
                                                   "probabilidad_basica", "variacion_exponencial")))
  diversidad_respuestas <- respuestas_unicas / length(respuestas)
  diversidad_hashes <- hashes_unicos / length(datos_ejercicios)
  
  # Diversidad promedio
  diversidad_promedio <- mean(c(diversidad_tipos, diversidad_respuestas, diversidad_hashes))
  
  list(
    diversidad_total = diversidad_promedio,
    diversidad_tipos = diversidad_tipos,
    diversidad_respuestas = diversidad_respuestas,
    diversidad_hashes = diversidad_hashes,
    tipos_ejercicios_unicos = tipos_unicos,
    respuestas_unicas = respuestas_unicas,
    ejercicios_totalmente_unicos = hashes_unicos,
    aprobado = diversidad_promedio >= 0.8,
    recomendacion = if (diversidad_promedio >= 0.9) {
      "Excelente diversidad en los ejercicios"
    } else if (diversidad_promedio >= 0.7) {
      "Buena diversidad, pero se puede mejorar"
    } else {
      "Diversidad insuficiente, revisar algoritmos de generación"
    }
  )
}

#' Generar reporte de calidad
#' 
#' Genera un reporte completo de calidad para un conjunto de ejercicios
#' 
#' @param datos_ejercicios Lista de ejercicios a analizar
#' @param incluir_detalles Incluir análisis detallado por ejercicio
#' @return Lista con reporte completo de calidad
#' @export
#' @examples
#' ejercicios <- ejecutar_generacion_datos("movimiento_lineal", 20)
#' reporte <- generar_reporte_calidad(ejercicios)
generar_reporte_calidad <- function(datos_ejercicios, incluir_detalles = FALSE) {
  
  inicio_tiempo <- Sys.time()
  
  if (length(datos_ejercicios) == 0) {
    return(list(error = "No hay ejercicios para analizar"))
  }
  
  # Ejecutar todos los tests
  resultados_tests <- ejecutar_tests_unitarios(datos_ejercicios)
  
  # Validación individual de cada ejercicio
  validaciones <- lapply(datos_ejercicios, validar_datos_icfes)
  calidades <- sapply(validaciones, function(v) v$calidad_total)
  aprobados <- sapply(validaciones, function(v) v$aprobado)
  
  # Estadísticas generales
  estadisticas <- list(
    total_ejercicios = length(datos_ejercicios),
    ejercicios_aprobados = sum(aprobados),
    tasa_aprobacion = mean(aprobados),
    calidad_promedio = mean(calidades, na.rm = TRUE),
    calidad_mediana = median(calidades, na.rm = TRUE),
    calidad_minima = min(calidades, na.rm = TRUE),
    calidad_maxima = max(calidades, na.rm = TRUE),
    desviacion_calidad = sd(calidades, na.rm = TRUE)
  )
  
  # Análisis por tipo de ejercicio
  tipos <- sapply(datos_ejercicios, function(x) x$tipo %||% "desconocido")
  analisis_tipos <- lapply(unique(tipos), function(tipo) {
    indices_tipo <- which(tipos == tipo)
    list(
      tipo = tipo,
      cantidad = length(indices_tipo),
      calidad_promedio = mean(calidades[indices_tipo], na.rm = TRUE),
      tasa_aprobacion = mean(aprobados[indices_tipo])
    )
  })
  names(analisis_tipos) <- unique(tipos)
  
  # Recomendaciones
  recomendaciones <- generar_recomendaciones_calidad(estadisticas, resultados_tests)
  
  # Construir reporte final
  reporte <- list(
    resumen_ejecutivo = list(
      calificacion_general = calcular_calificacion_general(estadisticas, resultados_tests),
      principales_fortalezas = identificar_fortalezas(estadisticas, resultados_tests),
      areas_mejora = identificar_areas_mejora(estadisticas, resultados_tests)
    ),
    estadisticas_generales = estadisticas,
    resultados_tests = resultados_tests,
    analisis_por_tipo = analisis_tipos,
    recomendaciones = recomendaciones,
    metadatos = list(
      timestamp_reporte = Sys.time(),
      tiempo_analisis = as.numeric(Sys.time() - inicio_tiempo, units = "secs"),
      version_paquete = utils::packageVersion("ICFESMathExams")
    )
  )
  
  # Incluir detalles si se solicita
  if (incluir_detalles) {
    reporte$detalles_ejercicios <- mapply(function(ejercicio, validacion, indice) {
      list(
        ejercicio_id = indice,
        tipo = ejercicio$tipo,
        calidad = validacion$calidad_total,
        aprobado = validacion$aprobado,
        problemas = c(validacion$matematica$errores, validacion$pedagogica$errores,
                     validacion$tecnica$errores, validacion$contextual$errores)
      )
    }, datos_ejercicios, validaciones, seq_along(datos_ejercicios), SIMPLIFY = FALSE)
  }
  
  return(reporte)
}

# Funciones auxiliares para el reporte de calidad

determinar_estado_general <- function(resultados) {
  if (all(sapply(resultados[c("consistencia", "diversidad")], function(r) r$aprobado %||% TRUE))) {
    "EXCELENTE"
  } else if (any(sapply(resultados[c("consistencia", "diversidad")], function(r) r$aprobado %||% TRUE))) {
    "BUENO"
  } else {
    "NECESITA_MEJORA"
  }
}

calcular_calificacion_general <- function(estadisticas, tests) {
  puntuacion <- 0
  
  # Calidad promedio (40% del peso)
  puntuacion <- puntuacion + (estadisticas$calidad_promedio * 0.4)
  
  # Tasa de aprobación (30% del peso)
  puntuacion <- puntuacion + (estadisticas$tasa_aprobacion * 0.3)
  
  # Consistencia matemática (20% del peso)
  if (!is.null(tests$consistencia)) {
    puntuacion <- puntuacion + (tests$consistencia$consistencia_matematica * 0.2)
  }
  
  # Diversidad (10% del peso)
  if (!is.null(tests$diversidad)) {
    puntuacion <- puntuacion + (tests$diversidad$diversidad_total * 0.1)
  }
  
  # Convertir a escala 1-5
  calificacion_numerica <- round(puntuacion * 5, 1)
  
  calificacion_texto <- if (calificacion_numerica >= 4.5) {
    "Excepcional"
  } else if (calificacion_numerica >= 4.0) {
    "Excelente" 
  } else if (calificacion_numerica >= 3.5) {
    "Muy Bueno"
  } else if (calificacion_numerica >= 3.0) {
    "Bueno"
  } else if (calificacion_numerica >= 2.5) {
    "Regular"
  } else {
    "Necesita Mejora"
  }
  
  list(
    puntuacion = calificacion_numerica,
    texto = calificacion_texto,
    sobre_cinco = calificacion_numerica
  )
}

identificar_fortalezas <- function(estadisticas, tests) {
  fortalezas <- c()
  
  if (estadisticas$calidad_promedio >= 0.9) {
    fortalezas <- c(fortalezas, "Calidad promedio excepcional")
  }
  
  if (estadisticas$tasa_aprobacion >= 0.95) {
    fortalezas <- c(fortalezas, "Muy alta tasa de aprobación")
  }
  
  if (!is.null(tests$consistencia) && tests$consistencia$consistencia_matematica >= 0.98) {
    fortalezas <- c(fortalezas, "Excelente consistencia matemática")
  }
  
  if (!is.null(tests$diversidad) && tests$diversidad$diversidad_total >= 0.9) {
    fortalezas <- c(fortalezas, "Excelente diversidad de ejercicios")
  }
  
  return(fortalezas)
}

identificar_areas_mejora <- function(estadisticas, tests) {
  mejoras <- c()
  
  if (estadisticas$calidad_promedio < 0.8) {
    mejoras <- c(mejoras, "Mejorar calidad promedio de ejercicios")
  }
  
  if (estadisticas$tasa_aprobacion < 0.9) {
    mejoras <- c(mejoras, "Aumentar tasa de aprobación en validaciones")
  }
  
  if (!is.null(tests$consistencia) && tests$consistencia$consistencia_matematica < 0.95) {
    mejoras <- c(mejoras, "Corregir inconsistencias matemáticas")
  }
  
  if (!is.null(tests$diversidad) && tests$diversidad$diversidad_total < 0.8) {
    mejoras <- c(mejoras, "Aumentar diversidad de parámetros")
  }
  
  return(mejoras)
}

generar_recomendaciones_calidad <- function(estadisticas, tests) {
  recomendaciones <- c()
  
  if (estadisticas$calidad_promedio >= 0.9 && estadisticas$tasa_aprobacion >= 0.95) {
    recomendaciones <- c(recomendaciones, "✅ Los ejercicios están listos para uso en producción")
  }
  
  if (estadisticas$desviacion_calidad > 0.2) {
    recomendaciones <- c(recomendaciones, "⚠️ Revisar ejercicios con calidad muy variable")
  }
  
  if (!is.null(tests$diversidad) && tests$diversidad$diversidad_respuestas < 0.8) {
    recomendaciones <- c(recomendaciones, "🔄 Aumentar variabilidad en las respuestas generadas")
  }
  
  return(recomendaciones)
}