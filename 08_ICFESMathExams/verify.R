#!/usr/bin/env Rscript

# verify.R - Script de verificación para el paquete ICFESMathExams
# Ejecutar: Rscript verify.R

cat("🔍 Iniciando verificación del paquete ICFESMathExams...\n\n")

errores <- 0
advertencias <- 0

# Función para verificar existencia de archivos/directorios
verificar_existe <- function(ruta, descripcion, obligatorio = TRUE) {
  if (file.exists(ruta)) {
    cat("✅", descripcion, "\n")
    return(TRUE)
  } else {
    if (obligatorio) {
      cat("❌", descripcion, "- FALTANTE\n")
      errores <<- errores + 1
    } else {
      cat("⚠️ ", descripcion, "- OPCIONAL, no encontrado\n")
      advertencias <<- advertencias + 1
    }
    return(FALSE)
  }
}

# Verificar estructura básica del paquete
cat("📁 Verificando estructura del paquete:\n")
verificar_existe("DESCRIPTION", "Archivo DESCRIPTION")
verificar_existe("R/", "Directorio R/")
verificar_existe("tests/", "Directorio tests/")
verificar_existe("vignettes/", "Directorio vignettes/")
verificar_existe("README.md", "Archivo README.md")

# Verificar archivos R principales
cat("\n📄 Verificando archivos R principales:\n")
verificar_existe("R/utilidades.R", "Funciones de utilidades")
verificar_existe("R/generacion-datos.R", "Funciones de generación de datos")
verificar_existe("R/validacion.R", "Funciones de validación")
verificar_existe("R/testing-unitario.R", "Funciones de testing")
verificar_existe("R/zzz.R", "Funciones de inicialización")

# Verificar archivos de testing
cat("\n🧪 Verificando estructura de tests:\n")
verificar_existe("tests/testthat.R", "Configuración testthat")
verificar_existe("tests/testthat/", "Directorio de tests testthat")
verificar_existe("tests/testthat/test-generacion-datos.R", "Tests de generación")
verificar_existe("tests/testthat/test-validacion.R", "Tests de validación")
verificar_existe("tests/testthat/test-testing-unitario.R", "Tests del sistema de testing")

# Verificar vignettes
cat("\n📚 Verificando vignettes:\n")
verificar_existe("vignettes/introduccion.Rmd", "Vignette de introducción")
verificar_existe("vignettes/guia-avanzada.Rmd", "Vignette de guía avanzada")
verificar_existe("vignettes/casos-de-uso.Rmd", "Vignette de casos de uso")

# Verificar scripts de automatización
cat("\n🛠️  Verificando scripts de automatización:\n")
verificar_existe("build.R", "Script de construcción")
verificar_existe("clean.R", "Script de limpieza")
verificar_existe("verify.R", "Script de verificación (este archivo)")

# Verificar contenido del DESCRIPTION
if (file.exists("DESCRIPTION")) {
  cat("\n📋 Verificando contenido del DESCRIPTION:\n")
  desc_content <- readLines("DESCRIPTION")
  
  campos_requeridos <- c("Package:", "Title:", "Version:", "Author:", "Maintainer:", 
                        "Description:", "License:", "Depends:")
  
  for (campo in campos_requeridos) {
    if (any(grepl(paste0("^", campo), desc_content))) {
      cat("✅", campo, "presente\n")
    } else {
      cat("❌", campo, "FALTANTE\n")
      errores <- errores + 1
    }
  }
}

# Verificar que R/ contiene funciones exportables
if (file.exists("R/")) {
  cat("\n🔧 Verificando funciones R:\n")
  archivos_r <- list.files("R/", pattern = "\\.R$", full.names = TRUE)
  total_funciones <- 0
  
  for (archivo in archivos_r) {
    contenido <- readLines(archivo)
    funciones <- length(grep("^[a-zA-Z][a-zA-Z0-9_.]* *<- *function", contenido))
    total_funciones <- total_funciones + funciones
  }
  
  cat("✅ Total de funciones encontradas:", total_funciones, "\n")
  
  if (total_funciones == 0) {
    cat("❌ No se encontraron funciones en el directorio R/\n")
    errores <- errores + 1
  }
}

# Resumen final
cat("\n" , rep("=", 50), "\n")
cat("📊 RESUMEN DE VERIFICACIÓN:\n")
cat("✅ Verificaciones exitosas\n")
cat("❌ Errores encontrados:", errores, "\n")
cat("⚠️  Advertencias:", advertencias, "\n")

if (errores == 0) {
  cat("\n🎉 ¡PAQUETE VERIFICADO EXITOSAMENTE!")
  cat("\n✨ El paquete ICFESMathExams está listo para construcción e instalación.\n")
} else {
  cat("\n🚨 SE ENCONTRARON ERRORES")
  cat("\n🔧 Por favor, corrija los errores antes de proceder con la construcción.\n")
}

# Retornar código de salida apropiado
if (errores > 0) {
  quit(status = 1)
} else {
  quit(status = 0)
}