Package: ICFESMathExams
Type: Package
Title: Generación Automatizada de Exámenes de Matemáticas ICFES
Version: 3.0.0
Author: Autor Principal <<EMAIL>>
Maintainer: Autor Principal <<EMAIL>>
Description: Paquete R profesional para la generación automatizada de exámenes
    de matemáticas alineados con los estándares ICFES. Ofrece funcionalidades
    avanzadas para generación de datos con randomización sofisticada (300+ versiones
    únicas), validación pedagógica multi-dimensional, tests unitarios automatizados,
    integración nativa con r-exams, análisis psicométrico avanzado (IRT),
    exámenes adaptativos, integración con LMS (Moodle, Canvas, Blackboard),
    exportación múltiple (PDF, HTML, XML, SCORM, QTI) e integración multi-lenguaje
    (R, LaTeX, Python, TikZ). Desarrollado a partir del proyecto
    proyecto-r-exams-icfes-matematicas-optimizado.
License: MIT + file LICENSE
URL: https://github.com/usuario/ICFESMathExams
BugReports: https://github.com/usuario/ICFESMathExams/issues
Encoding: UTF-8
LazyData: true
Roxygen: list(markdown = TRUE)
RoxygenNote: 7.2.3
VignetteBuilder: knitr
SystemRequirements: LaTeX (texlive-full recommended), Python (>= 3.6) with matplotlib and numpy, TikZ/PGF
Depends: 
    R (>= 4.0.0)
Imports:
    utils,
    stats,
    methods,
    parallel,
    digest,
    rmarkdown,
    knitr,
    yaml,
    jsonlite,
    xml2
Suggests:
    exams (>= 2.4-0),
    ggplot2,
    dplyr,
    purrr,
    stringr,
    readr,
    tibble,
    magrittr,
    reticulate,
    bookdown,
    tinytex,
    futile.logger,
    memoise,
    future,
    furrr,
    plotly,
    DT,
    shiny,
    psychometric,
    ltm,
    mirt,
    testthat
Suggests:
    testthat (>= 3.0.0),
    knitr,
    covr,
    pkgdown,
    devtools,
    roxygen2
Config/testthat/edition: 3