# Generated by roxygen2: do not edit by hand

# Importaciones principales
import(utils)
import(stats)
import(methods)
import(parallel)

# Importaciones selectivas
importFrom(digest, digest)
importFrom(ggplot2, ggplot, aes, geom_point, geom_line, labs, theme_minimal)
importFrom(dplyr, filter, select, mutate, summarise, group_by, arrange, %>%)
importFrom(purrr, map, map_dbl, map_chr, map_lgl)
importFrom(stringr, str_detect, str_replace, str_extract, str_split)
importFrom(readr, read_csv, write_csv)
importFrom(tibble, tibble, as_tibble)
importFrom(magrittr, "%>%")
importFrom(rmarkdown, render)
importFrom(knitr, kable)
importFrom(yaml, yaml.load, as.yaml)
importFrom(jsonlite, toJSON, fromJSON)
importFrom(xml2, read_xml, write_xml, xml_new_root, xml_add_child)

# Exportaciones principales - Funciones básicas
export(generar_datos)
export(ejecutar_generacion_datos)
export(validar_ejercicio)
export(generar_configuracion_defecto)

# Exportaciones - Nuevas funcionalidades v3.0.0
export(inicializar_icfes_optimizado)
export(obtener_configuracion)
export(actualizar_configuracion)

# Exportaciones - Integración r-exams
export(crear_template_rexams)
export(exportar_rexams)
export(crear_examen_completo_rexams)
export(validar_compatibilidad_rexams)

# Exportaciones - Ejercicios avanzados
export(generar_geometria_analitica)
export(generar_funciones_matematicas)
export(generar_estadistica_avanzada)
export(generar_algebra_avanzada)

# Exportaciones - Análisis psicométrico
export(analizar_psicometria_completa)

# Exportaciones - Adaptabilidad
export(generar_examen_adaptativo)
export(personalizar_examen_estudiante)
export(generar_recomendaciones_inteligentes)

# Exportaciones - Integración LMS
export(exportar_lms)
export(configurar_integracion_lms)
export(generar_reporte_compatibilidad_lms)

# Exportaciones - Utilidades
export(generar_reporte_validacion)
export(ejecutar_tests_unitarios)
export(generar_estadisticas_generacion)

# Métodos S3
S3method(print, examen_adaptativo_icfes)
S3method(print, examen_personalizado_icfes)
S3method(print, psicometria_icfes)
S3method(print, exportacion_lms_icfes)
S3method(print, recomendaciones_icfes)
S3method(print, reporte_compatibilidad_lms)
S3method(summary, examen_adaptativo_icfes)
S3method(summary, psicometria_icfes)
