# ICFESMathExams 📊

> **Paquete R para Generación, Validación y Análisis de Exámenes de Matemáticas ICFES**

[![R Version](https://img.shields.io/badge/R-%E2%89%A54.0.0-blue.svg)](https://www.r-project.org/)
[![License](https://img.shields.io/badge/License-GPL%20v3-green.svg)](https://www.gnu.org/licenses/gpl-3.0)
[![Status](https://img.shields.io/badge/Status-Stable-brightgreen.svg)]()

## 🎯 ¿Qué es ICFESMathExams?

ICFESMathExams es un paquete R integral que transforma la creación y análisis de exámenes de matemáticas, basado en los estándares del ICFES (Instituto Colombiano para la Evaluación de la Educación). Permite generar miles de versiones únicas de exámenes, validar su calidad pedagógica y realizar análisis estadísticos avanzados.

## ✨ Características Principales

### 🚀 Generación Inteligente
- **Aleatorización Controlada**: Números, parámetros y contextos únicos para cada versión
- **Coherencia Matemática**: Garantiza soluciones válidas y razonables
- **Progresión de Dificultad**: Niveles adaptativos automáticos

### 📋 Validación Integral
- **Revisión Pedagógica**: Verifica alineación con estándares curriculares
- **Control de Calidad**: Detecta errores e inconsistencias matemáticas
- **Análisis Psicométrico**: Evalúa dificultad, discriminación y confiabilidad

### 🎨 Personalización Avanzada
- **Configuración Flexible**: Adapta a diferentes instituciones y contextos
- **Múltiples Formatos**: Exporta a PDF, HTML, Word, LaTeX
- **Integración LMS**: Compatible con Moodle, Canvas, Blackboard

### 📊 Análisis Sofisticado
- **Teoría de Respuesta al Ítem (IRT)**
- **Detección de Funcionamiento Diferencial**
- **Análisis Longitudinal de Rendimiento**
- **Machine Learning Predictivo**

## 🚀 Instalación

### Prerrequisitos

```r
# Instalar dependencias del sistema (Ubuntu/Debian)
sudo apt update
sudo apt install -y r-base r-base-dev libcurl4-openssl-dev libssl-dev libxml2-dev

# En R, instalar dependencias
install.packages(c("devtools", "testthat", "knitr", "rmarkdown"))
```

### Instalación del Paquete

```r
# Desde directorio local
devtools::install_local("path/to/ICFESMathExams")

# O si ya estás en el directorio del paquete
devtools::install()
```

## 📖 Inicio Rápido

### Ejemplo Básico

```r
library(ICFESMathExams)

# 1. Configuración inicial
config <- crear_configuracion_basica(
  nivel = "grado_11",
  areas = c("algebra", "geometria", "calculo"),
  num_ejercicios = 25,
  num_versiones = 50
)

# 2. Generar examen
examen <- generar_datos_icfes(config)

# 3. Validar calidad
validacion <- validar_datos_icfes(examen)

# 4. Exportar resultados
exportar_resultados(examen, formato = "pdf")
```

### Ejemplo Avanzado

```r
# Configuración completa para institución
config_avanzada <- crear_configuracion_completa(
  nivel_educativo = "grado_11",
  areas_matematicas = c("algebra", "geometria", "trigonometria", "calculo"),
  competencias = c("interpretacion", "argumentacion", "resolucion"),
  num_ejercicios_por_area = 8,
  num_versiones_objetivo = 200,
  criterios_validacion = list(
    coherencia_matematica = TRUE,
    progresion_dificultad = TRUE,
    diversidad_contextos = TRUE
  )
)

# Generación y análisis completo
resultados <- ejecutar_generacion_completa(config_avanzada)
analisis <- ejecutar_analisis_irt(resultados)
reporte <- generar_reporte_completo(resultados, analisis)
```

## 📚 Documentación

### Vignettes Disponibles

- **[Introducción](vignettes/introduccion.html)**: Conceptos básicos y primeros pasos
- **[Guía Avanzada](vignettes/guia-avanzada.html)**: Funcionalidades especializadas
- **[Casos de Uso](vignettes/casos-de-uso.html)**: Ejemplos por perfil de usuario

### Funciones Principales

```r
# Ver todas las funciones disponibles
help(package = "ICFESMathExams")

# Funciones principales por módulo
?crear_configuracion_basica      # Configuración
?generar_datos_icfes             # Generación
?validar_datos_icfes             # Validación
?ejecutar_analisis_irt           # Análisis estadístico
?exportar_resultados             # Exportación
```

## 🎯 Casos de Uso por Perfil

### 👨‍🏫 Docentes
- Evaluaciones trimestrales personalizadas
- Diagnósticos de necesidades de aprendizaje
- Generación de ejercicios de práctica

### 🏫 Coordinadores Académicos
- Simulacros institucionales ICFES
- Análisis longitudinal del rendimiento
- Reportes para directivos

### 🎓 Universidades
- Exámenes de admisión
- Programas de nivelación
- Evaluación de competencias

### 🔬 Investigadores
- Estudios de equidad educativa
- Validación curricular
- Análisis psicométricos

## 🛠️ Scripts de Automatización

El paquete incluye scripts para facilitar su uso:

```bash
# Construir e instalar el paquete
Rscript build.R

# Limpiar archivos temporales
Rscript clean.R

# Verificar integridad del paquete
Rscript verify.R

# Diagnosticar problemas comunes
Rscript diagnostico.R
```

## 📊 Estructura del Paquete

```
ICFESMathExams/
├── R/                          # Código fuente
│   ├── utilidades.R           # Funciones auxiliares (9 funciones)
│   ├── generacion-datos.R     # Generación matemática (8 funciones)
│   ├── validacion.R           # Control de calidad (8 funciones)
│   ├── testing-unitario.R     # Análisis estadístico (4 funciones)
│   └── zzz.R                  # Inicialización (5 funciones)
├── tests/                      # Tests unitarios
│   ├── testthat.R
│   └── testthat/
├── vignettes/                  # Documentación extensa
│   ├── introduccion.Rmd
│   ├── guia-avanzada.Rmd
│   └── casos-de-uso.Rmd
├── man/                        # Documentación de funciones
├── DESCRIPTION                 # Metadatos del paquete
└── README.md                   # Este archivo
```

## 🧪 Testing

```r
# Ejecutar todos los tests
devtools::test()

# Tests específicos
testthat::test_file("tests/testthat/test-generacion-datos.R")

# Verificar cobertura
covr::package_coverage()
```

## 🔧 Configuración del Entorno

### Para Manjaro/Arch Linux

```bash
# Instalar R y dependencias
sudo pacman -S r gcc-fortran blas lapack

# Instalar LaTeX (para vignettes)
sudo pacman -S texlive-core texlive-latexextra

# En R
install.packages(c("devtools", "testthat", "knitr"))
```

### Para Windows

```r
# Instalar Rtools
# Descargar desde: https://cran.r-project.org/bin/windows/Rtools/

# En R
install.packages(c("devtools", "testthat", "knitr"))
```

## 📈 Rendimiento y Escalabilidad

- **Generación Masiva**: Hasta 10,000 versiones en procesamiento paralelo
- **Memoria Optimizada**: Gestión inteligente para datasets grandes
- **Procesamiento Paralelo**: Utiliza múltiples núcleos automáticamente
- **Cache Inteligente**: Reutiliza cálculos para mayor eficiencia

## 🤝 Contribuciones

El paquete está diseñado para ser extensible. Para contribuir:

1. Fork el repositorio
2. Crea una rama para tu feature
3. Añade tests para nueva funcionalidad
4. Asegúrate que todos los tests pasen
5. Envía un pull request

## 📄 Licencia

Este proyecto está licenciado bajo GPL v3 - ver el archivo LICENSE para detalles.

## 📞 Soporte

- **Documentación**: Consulta los vignettes incluidos
- **Problemas**: Usa las funciones de diagnóstico integradas
- **Errores**: Reporta issues en el repositorio
- **Consultas**: Contacta al equipo de desarrollo

## 🏆 Reconocimientos

Desarrollado como parte del proyecto de optimización de exámenes ICFES de matemáticas, basado en el trabajo original del repositorio [proyecto-r-exams-icfes-matematicas-optimizado](https://github.com/alvaretto/proyecto-r-exams-icfes-matematicas-optimizado).

---

**ICFESMathExams v1.0.0** - Transformando la evaluación educativa en Colombia 🇨🇴