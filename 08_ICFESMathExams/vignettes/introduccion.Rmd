---
title: "Introducción al Paquete ICFESMathExams"
author: "ICFESMathExams Package"
date: "`r Sys.Date()`"
output: rmarkdown::html_vignette
vignette: >
  %\VignetteIndexEntry{Introducción al Paquete ICFESMathExams}
  %\VignetteEngine{knitr::rmarkdown}
  %\VignetteEncoding{UTF-8}
---

```{r setup, include = FALSE}
knitr::opts_chunk$set(
  collapse = TRUE,
  comment = "#>"
)
library(ICFESMathExams)
```

# Bienvenido a ICFESMathExams

El paquete **ICFESMathExams** es una solución integral para la generación, validación y análisis de exámenes de matemáticas basados en los estándares del ICFES (Instituto Colombiano para la Evaluación de la Educación).

## ¿Qué es ICFESMathExams?

ICFESMathExams transforma el proceso tradicional de creación de exámenes en un sistema automatizado, científico y escalable que permite:

- **Generación Masiva**: Crear miles de versiones únicas de exámenes
- **Validación Pedagógica**: Garantizar calidad y coherencia educativa
- **Análisis Estadístico**: Evaluar dificultad, discriminación y fiabilidad
- **Personalización**: Adaptar contenido a diferentes niveles y contextos

## Instalación

```{r eval=FALSE}
# Desde un archivo local
devtools::install_local("ruta/al/paquete/ICFESMathExams")

# O si ya está en tu directorio de trabajo
devtools::install()
```

## Primer Ejemplo

Veamos cómo generar tu primer conjunto de datos para exámenes:

```{r eval=FALSE}
# Cargar el paquete
library(ICFESMathExams)

# Configurar parámetros básicos
config <- crear_configuracion_basica(
  nivel = "grado_11",
  areas = c("algebra", "geometria", "calculo"),
  num_ejercicios = 25,
  num_versiones = 100
)

# Generar datos
datos_examen <- generar_datos_icfes(config)

# Ver resumen
resumen_datos(datos_examen)
```

## Estructura del Paquete

### Módulos Principales

1. **Utilidades** (`utilidades.R`): Funciones auxiliares y configuración
2. **Generación** (`generacion-datos.R`): Creación de datos matemáticos
3. **Validación** (`validacion.R`): Control de calidad y coherencia
4. **Testing** (`testing-unitario.R`): Análisis estadístico y psicométrico
5. **Inicialización** (`zzz.R`): Configuración del paquete

### Flujo de Trabajo Típico

```{r eval=FALSE}
# 1. Configuración inicial
config <- crear_configuracion_completa()

# 2. Generación de datos
datos <- ejecutar_generacion_datos(config)

# 3. Validación
resultado_validacion <- validar_datos_icfes(datos)

# 4. Análisis estadístico
analisis <- ejecutar_analisis_completo(datos)

# 5. Exportación
exportar_resultados(datos, analisis, formato = "html")
```

## Características Principales

### 🎯 Generación Inteligente

- **Aleatorización Controlada**: Números, parámetros y contextos únicos
- **Coherencia Matemática**: Garantiza soluciones válidas
- **Progresión de Dificultad**: Niveles adaptativos automáticos

### 📊 Validación Integral

- **Revisión Pedagógica**: Verifica estándares educativos
- **Control de Calidad**: Detecta errores y inconsistencias
- **Análisis Psicométrico**: Evalúa propiedades estadísticas

### 🔧 Personalización Avanzada

- **Configuración Flexible**: Adapta a diferentes instituciones
- **Temas Customizables**: Ajusta contenido por región/contexto
- **Formatos Múltiples**: Exporta a PDF, HTML, Word, LMS

## Casos de Uso

### Para Educadores

```{r eval=FALSE}
# Crear examen para evaluación trimestral
examen_trimestral <- crear_examen_personalizado(
  grado = 11,
  temas = c("funciones", "trigonometria"),
  dificultad = "intermedio",
  tiempo_estimado = 90  # minutos
)
```

### Para Instituciones

```{r eval=FALSE}
# Generación masiva para simulacro ICFES
simulacro <- generar_simulacro_masivo(
  cantidad_estudiantes = 500,
  areas_evaluacion = "todas",
  incluir_analisis = TRUE
)
```

### Para Investigadores

```{r eval=FALSE}
# Análisis comparativo de dificultad
estudio <- comparar_dificultad_items(
  datos_historicos = cargar_datos_2019_2023(),
  nuevos_items = datos_examen,
  metodo = "rasch"
)
```

## Próximos Pasos

- **[Guía Avanzada](guia-avanzada.html)**: Funcionalidades especializadas
- **[Casos de Uso](casos-de-uso.html)**: Ejemplos detallados por perfil
- **Documentación de Funciones**: `help(package = "ICFESMathExams")`

## Soporte y Contribuciones

¿Encontraste un problema o tienes una sugerencia? 

- Revisa la documentación completa con `help(package = "ICFESMathExams")`
- Consulta ejemplos adicionales en los otros vignettes
- Usa las funciones de diagnóstico integradas para resolver problemas comunes

---

*ICFESMathExams v1.0.0 - Transformando la evaluación educativa en Colombia*