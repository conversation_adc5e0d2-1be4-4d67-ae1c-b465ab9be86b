#!/bin/bash

# verificar_prerrequisitos_manjaro.sh
# Script para verificar que todos los prerrequisitos estén instalados en Manjaro

echo "🔍 Verificador de Prerrequisitos - ICFESMathExams en Manjaro Plasma"
echo "=================================================================="

errores=0
advertencias=0

# Función para verificar comando
verificar_comando() {
    if command -v "$1" &> /dev/null; then
        echo "✅ $1 está instalado"
        if [ ! -z "$2" ]; then
            version=$($1 $2 2>&1 | head -1)
            echo "   Versión: $version"
        fi
    else
        echo "❌ $1 NO está instalado"
        echo "   Comando para instalar: sudo pacman -S $3"
        ((errores++))
    fi
}

# Función para verificar paquete pacman
verificar_paquete() {
    if pacman -Q "$1" &> /dev/null; then
        version=$(pacman -Q "$1" | awk '{print $2}')
        echo "✅ $1 está instalado (versión: $version)"
    else
        echo "❌ $1 NO está instalado"
        echo "   Comando para instalar: sudo pacman -S $1"
        ((errores++))
    fi
}

# Verificar herramientas básicas del sistema
echo "🔧 Verificando herramientas básicas del sistema:"
verificar_comando "gcc" "--version" "gcc"
verificar_comando "make" "--version" "make"
verificar_comando "git" "--version" "git"
verificar_comando "curl" "--version" "curl"
verificar_comando "wget" "--version" "wget"

echo ""
echo "📊 Verificando R y dependencias matemáticas:"
verificar_comando "R" "--version" "r"
verificar_paquete "gcc-fortran"
verificar_paquete "blas"
verificar_paquete "lapack"

echo ""
echo "📚 Verificando LaTeX (para vignettes):"
verificar_comando "pdflatex" "--version" "texlive-core"
verificar_paquete "texlive-latexextra"

echo ""
echo "🌐 Verificando bibliotecas de desarrollo:"
verificar_paquete "libxml2"
verificar_paquete "openssl"
verificar_paquete "pkg-config"

# Verificar espacio en disco
echo ""
echo "💾 Verificando espacio en disco:"
espacio_libre=$(df -h ~ | awk 'NR==2{print $4}')
echo "✅ Espacio libre en ~: $espacio_libre"

# Verificar si R puede cargar dependencias básicas
echo ""
echo "🧪 Verificando capacidades de R:"
if command -v R &> /dev/null; then
    echo "Probando carga de bibliotecas básicas..."
    
    # Test de devtools
    if R --slave -e "library(devtools)" 2>/dev/null; then
        echo "✅ devtools disponible"
    else
        echo "⚠️  devtools no está instalado (se puede instalar después)"
        ((advertencias++))
    fi
    
    # Test de compilación básica
    echo "Probando capacidad de compilación..."
    test_result=$(R --slave -e "
    tryCatch({
        tools:::.check_packages_used()
        cat('SUCCESS')
    }, error = function(e) cat('ERROR'))
    " 2>/dev/null)
    
    if [[ "$test_result" == *"SUCCESS"* ]]; then
        echo "✅ R puede compilar paquetes"
    else
        echo "⚠️  Posibles problemas de compilación en R"
        ((advertencias++))
    fi
else
    echo "❌ R no está disponible - instalar primero"
    ((errores++))
fi

# Verificar directorio de trabajo
echo ""
echo "📁 Verificando configuración de directorios:"
if [ -w ~ ]; then
    echo "✅ Directorio home escribible"
else
    echo "❌ Sin permisos de escritura en directorio home"
    ((errores++))
fi

# Crear directorio de prueba
test_dir="/tmp/icfes_test_$$"
if mkdir -p "$test_dir" 2>/dev/null; then
    echo "✅ Puede crear directorios temporales"
    rm -rf "$test_dir"
else
    echo "❌ No puede crear directorios temporales"
    ((errores++))
fi

# Resumen final
echo ""
echo "================================================"
echo "📊 RESUMEN DE VERIFICACIÓN:"
echo "✅ Verificaciones exitosas"
echo "❌ Errores encontrados: $errores"
echo "⚠️  Advertencias: $advertencias"

if [ $errores -eq 0 ]; then
    echo ""
    echo "🎉 ¡SISTEMA LISTO PARA INSTALAR ICFESMathExams!"
    echo "✨ Puedes proceder con la instalación del paquete."
    echo ""
    echo "Próximos pasos:"
    echo "1. Descargar ICFESMathExams_v1.0.0.tar.gz"
    echo "2. Ejecutar el script de instalación"
    echo "3. Seguir la guía de instalación completa"
else
    echo ""
    echo "🚨 SE ENCONTRARON ERRORES CRÍTICOS"
    echo "🔧 Por favor, instala los componentes faltantes antes de continuar."
    echo ""
    echo "Comando rápido para instalar todo lo necesario:"
    echo "sudo pacman -S r gcc-fortran blas lapack texlive-core texlive-latexextra \\"
    echo "               libxml2 openssl pkg-config git curl wget make base-devel"
fi

if [ $advertencias -gt 0 ]; then
    echo ""
    echo "ℹ️  Nota: Las advertencias no impiden la instalación, pero pueden"
    echo "   requerir pasos adicionales durante el proceso."
fi

exit $errores