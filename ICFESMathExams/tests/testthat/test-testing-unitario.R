# Tests para sistema de testing unitario

test_that("ejecutar_tests_unitarios funciona con datos válidos", {
  # Generar ejercicios de prueba
  ejercicios <- ejecutar_generacion_datos(
    tipos_ejercicios = "movimiento_lineal",
    num_versiones = 10,
    seed = 123
  )
  
  resultados <- ejecutar_tests_unitarios(ejercicios)
  
  expect_type(resultados, "list")
  expect_true("consistencia" %in% names(resultados))
  expect_true("diversidad" %in% names(resultados))
  expect_true("calidad" %in% names(resultados))
  expect_true("resumen" %in% names(resultados))
  
  expect_equal(resultados$resumen$total_ejercicios, 10)
})

test_that("ejecutar_tests_unitarios maneja lista vacía", {
  resultados <- ejecutar_tests_unitarios(list())
  
  expect_true("error" %in% names(resultados))
  expect_true(grepl("No hay datos", resultados$error))
})

test_that("testear_consistencia_matematica detecta errores", {
  # Crear ejercicios con errores deliberados
  ejercicios_malos <- list(
    list(
      tipo = "movimiento_lineal",
      velocidad = 30,
      tiempo = 2,
      respuesta_correcta = 100  # Error: debería ser 60
    ),
    list(
      tipo = "movimiento_lineal", 
      velocidad = 20,
      tiempo = 3,
      respuesta_correcta = 60   # Correcto: 20 * 3 = 60
    )
  )
  
  resultado_consistencia <- testear_consistencia_matematica(ejercicios_malos)
  
  expect_type(resultado_consistencia, "list")
  expect_true("consistencia_matematica" %in% names(resultado_consistencia))
  expect_true("ejercicios_inconsistentes" %in% names(resultado_consistencia))
  expect_true("aprobado" %in% names(resultado_consistencia))
  
  expect_equal(resultado_consistencia$ejercicios_inconsistentes, 1)
  expect_equal(resultado_consistencia$consistencia_matematica, 0.5)  # 1 de 2 correcto
  expect_false(resultado_consistencia$aprobado)  # No aprueba con <95%
})

test_that("testear_consistencia_matematica aprueba ejercicios correctos", {
  # Crear ejercicios correctos
  ejercicios_buenos <- list(
    list(
      tipo = "movimiento_lineal",
      velocidad = 30,
      tiempo = 2,
      respuesta_correcta = 60
    ),
    list(
      tipo = "estadistica_descriptiva",
      muestra = c(10, 20, 30),
      respuesta_correcta = 20  # Media correcta
    )
  )
  
  resultado_consistencia <- testear_consistencia_matematica(ejercicios_buenos)
  
  expect_equal(resultado_consistencia$ejercicios_inconsistentes, 0)
  expect_equal(resultado_consistencia$consistencia_matematica, 1.0)
  expect_true(resultado_consistencia$aprobado)
})

test_that("testear_diversidad_parametros evalúa diversidad", {
  # Ejercicios con poca diversidad (mismo tipo, mismas respuestas)
  ejercicios_monotonos <- replicate(5, list(
    tipo = "movimiento_lineal",
    respuesta_correcta = 60,
    version_hash = "mismo_hash"
  ), simplify = FALSE)
  
  resultado_diversidad <- testear_diversidad_parametros(ejercicios_monotonos)
  
  expect_type(resultado_diversidad, "list")
  expect_true("diversidad_total" %in% names(resultado_diversidad))
  expect_true("diversidad_respuestas" %in% names(resultado_diversidad))
  expect_true("diversidad_hashes" %in% names(resultado_diversidad))
  
  # Debería tener baja diversidad
  expect_true(resultado_diversidad$diversidad_respuestas <= 0.5)
  expect_true(resultado_diversidad$diversidad_hashes <= 0.5)
  expect_false(resultado_diversidad$aprobado)
})

test_that("testear_diversidad_parametros con ejercicios diversos", {
  # Generar ejercicios diversos
  ejercicios_diversos <- ejecutar_generacion_datos(
    tipos_ejercicios = c("movimiento_lineal", "estadistica_descriptiva"),
    num_versiones = 6,
    seed = 456
  )
  
  resultado_diversidad <- testear_diversidad_parametros(ejercicios_diversos)
  
  # Debería tener alta diversidad
  expect_true(resultado_diversidad$diversidad_total >= 0.5)
  expect_true(resultado_diversidad$diversidad_hashes >= 0.8)  # Cada ejercicio único
})

test_that("testear_diversidad_parametros maneja casos límite", {
  # Un solo ejercicio
  resultado_uno <- testear_diversidad_parametros(list(
    list(tipo = "movimiento_lineal", respuesta_correcta = 60)
  ))
  
  expect_true("error" %in% names(resultado_uno))
  expect_equal(resultado_uno$diversidad, 0)
  
  # Lista vacía
  resultado_vacio <- testear_diversidad_parametros(list())
  expect_true("error" %in% names(resultado_vacio))
})

test_that("generar_reporte_calidad crea reporte completo", {
  # Generar ejercicios de prueba
  ejercicios <- ejecutar_generacion_datos(
    tipos_ejercicios = c("movimiento_lineal", "estadistica_descriptiva"),
    num_versiones = 8,
    seed = 789
  )
  
  reporte <- generar_reporte_calidad(ejercicios)
  
  expect_type(reporte, "list")
  expect_true("resumen_ejecutivo" %in% names(reporte))
  expect_true("estadisticas_generales" %in% names(reporte))
  expect_true("resultados_tests" %in% names(reporte))
  expect_true("analisis_por_tipo" %in% names(reporte))
  expect_true("recomendaciones" %in% names(reporte))
  expect_true("metadatos" %in% names(reporte))
  
  # Verificar estadísticas generales
  stats <- reporte$estadisticas_generales
  expect_equal(stats$total_ejercicios, 8)
  expect_true(stats$tasa_aprobacion >= 0 && stats$tasa_aprobacion <= 1)
  expect_true(stats$calidad_promedio >= 0 && stats$calidad_promedio <= 1)
  
  # Verificar resumen ejecutivo
  resumen <- reporte$resumen_ejecutivo
  expect_true("calificacion_general" %in% names(resumen))
  expect_true("principales_fortalezas" %in% names(resumen))
  expect_true("areas_mejora" %in% names(resumen))
})

test_that("generar_reporte_calidad con detalles", {
  ejercicios <- ejecutar_generacion_datos(
    tipos_ejercicios = "movimiento_lineal",
    num_versiones = 3,
    seed = 999
  )
  
  reporte_detallado <- generar_reporte_calidad(ejercicios, incluir_detalles = TRUE)
  
  expect_true("detalles_ejercicios" %in% names(reporte_detallado))
  expect_length(reporte_detallado$detalles_ejercicios, 3)
  
  # Verificar estructura de detalles
  detalle <- reporte_detallado$detalles_ejercicios[[1]]
  expect_true("ejercicio_id" %in% names(detalle))
  expect_true("tipo" %in% names(detalle))
  expect_true("calidad" %in% names(detalle))
  expect_true("aprobado" %in% names(detalle))
})

test_that("generar_reporte_calidad maneja lista vacía", {
  reporte_vacio <- generar_reporte_calidad(list())
  
  expect_true("error" %in% names(reporte_vacio))
  expect_true(grepl("No hay ejercicios", reporte_vacio$error))
})

test_that("funciones auxiliares de calificación", {
  # Test datos simulados para funciones auxiliares
  stats_test <- list(
    calidad_promedio = 0.9,
    tasa_aprobacion = 0.95,
    desviacion_calidad = 0.1
  )
  
  tests_test <- list(
    consistencia = list(consistencia_matematica = 0.98, aprobado = TRUE),
    diversidad = list(diversidad_total = 0.9, aprobado = TRUE)
  )
  
  # Las funciones auxiliares están definidas en el archivo principal
  # pero no son exportadas, así que no podemos testearlas directamente
  # Sin embargo, su funcionamiento se verifica a través de generar_reporte_calidad
  
  reporte_test <- list(
    estadisticas_generales = stats_test,
    resultados_tests = tests_test
  )
  
  # Verificar que el reporte se puede generar sin errores
  expect_type(reporte_test, "list")
})

test_that("integración completa del sistema de testing", {
  # Test de flujo completo
  ejercicios <- ejecutar_generacion_datos(
    tipos_ejercicios = c("movimiento_lineal", "estadistica_descriptiva", "probabilidad_basica"),
    num_versiones = 15,
    seed = 2024
  )
  
  # Ejecutar todos los tests
  resultados_completos <- ejecutar_tests_unitarios(ejercicios)
  
  # Generar reporte completo
  reporte_completo <- generar_reporte_calidad(ejercicios, incluir_detalles = TRUE)
  
  # Verificaciones finales
  expect_true(resultados_completos$resumen$total_ejercicios == 15)
  expect_true(reporte_completo$estadisticas_generales$total_ejercicios == 15)
  expect_length(reporte_completo$detalles_ejercicios, 15)
  
  # Verificar que hay al menos 2 tipos diferentes
  tipos_en_reporte <- names(reporte_completo$analisis_por_tipo)
  expect_true(length(tipos_en_reporte) >= 2)
  
  # Verificar metadatos
  expect_true("timestamp_reporte" %in% names(reporte_completo$metadatos))
  expect_true("tiempo_analisis" %in% names(reporte_completo$metadatos))
})