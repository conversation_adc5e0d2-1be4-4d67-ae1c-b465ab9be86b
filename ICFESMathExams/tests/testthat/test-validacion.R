# Tests para funciones de validación

test_that("validar_datos_icfes funciona con datos válidos", {
  # Generar datos de prueba
  datos <- generar_datos("movimiento_lineal")
  validacion <- validar_datos_icfes(datos)
  
  expect_type(validacion, "list")
  expect_true("aprobado" %in% names(validacion))
  expect_true("calidad_total" %in% names(validacion))
  expect_true("matematica" %in% names(validacion))
  expect_true("pedagogica" %in% names(validacion))
  expect_true("tecnica" %in% names(validacion))
  expect_true("contextual" %in% names(validacion))
  
  expect_type(validacion$aprobado, "logical")
  expect_type(validacion$calidad_total, "double")
  expect_true(validacion$calidad_total >= 0 && validacion$calidad_total <= 1)
})

test_that("validar_datos_icfes rechaza datos vacíos", {
  validacion <- validar_datos_icfes(list())
  
  expect_false(validacion$aprobado)
  expect_equal(validacion$calidad_total, 0)
  expect_true("error" %in% names(validacion))
})

test_that("validar_matematica detecta inconsistencias", {
  # Crear datos con inconsistencia deliberada
  datos_malos <- list(
    tipo = "movimiento_lineal",
    velocidad = 30,
    tiempo = 2,
    respuesta_correcta = 100  # Debería ser 60
  )
  
  validacion_mat <- validar_matematica(datos_malos)
  
  expect_false(validacion_mat$valido)
  expect_true(length(validacion_mat$errores) > 0)
  expect_true(validacion_mat$puntuacion < 1.0)
})

test_that("validar_matematica aprueba datos correctos", {
  datos_buenos <- list(
    tipo = "movimiento_lineal",
    velocidad = 30,
    tiempo = 2,
    respuesta_correcta = 60
  )
  
  validacion_mat <- validar_matematica(datos_buenos)
  
  expect_true(validacion_mat$valido)
  expect_length(validacion_mat$errores, 0)
  expect_equal(validacion_mat$puntuacion, 1.0)
})

test_that("validar_pedagogica verifica enunciado", {
  # Datos sin enunciado
  datos_sin_enunciado <- list(
    tipo = "movimiento_lineal",
    respuesta_correcta = 60
  )
  
  validacion_ped <- validar_pedagogica(datos_sin_enunciado)
  
  expect_false(validacion_ped$valido)
  expect_true(any(grepl("enunciado", validacion_ped$errores, ignore.case = TRUE)))
  
  # Datos con buen enunciado
  datos_con_enunciado <- list(
    tipo = "movimiento_lineal",
    enunciado = "Un objeto se mueve con velocidad de 30 m/s durante 2 segundos. ¿Cuál es la distancia?",
    respuesta_correcta = 60,
    contexto = "Transporte en Colombia"
  )
  
  validacion_ped2 <- validar_pedagogica(datos_con_enunciado)
  expect_true(validacion_ped2$valido)
  expect_length(validacion_ped2$errores, 0)
})

test_that("validar_tecnica verifica estructura", {
  # Datos incompletos
  datos_incompletos <- list(
    respuesta_correcta = 60
    # Falta tipo y enunciado
  )
  
  validacion_tec <- validar_tecnica(datos_incompletos)
  
  expect_false(validacion_tec$valido)
  expect_true(length(validacion_tec$errores) > 0)
  
  # Datos completos
  datos_completos <- list(
    tipo = "movimiento_lineal",
    enunciado = "Test question",
    respuesta_correcta = 60,
    version_hash = "abc123",
    timestamp_generacion = Sys.time()
  )
  
  validacion_tec2 <- validar_tecnica(datos_completos)
  expect_true(validacion_tec2$valido)
  expect_length(validacion_tec2$errores, 0)
})

test_that("validar_contextual evalúa contexto colombiano", {
  # Datos sin contexto colombiano
  datos_sin_contexto <- list(
    enunciado = "A car moves at 30 m/s for 2 seconds",
    contexto = "Generic context"
  )
  
  validacion_ctx <- validar_contextual(datos_sin_contexto)
  expect_true(length(validacion_ctx$advertencias) > 0)
  
  # Datos con contexto colombiano
  datos_con_contexto <- list(
    enunciado = "Un automóvil viaja por una carretera colombiana a 30 m/s",
    contexto = "Transporte en Colombia"
  )
  
  validacion_ctx2 <- validar_contextual(datos_con_contexto)
  expect_true(validacion_ctx2$valido)
})

test_that("validar_lote_versiones procesa múltiples ejercicios", {
  # Generar lote de ejercicios
  ejercicios <- ejecutar_generacion_datos(
    tipos_ejercicios = "movimiento_lineal",
    num_versiones = 5,
    seed = 123
  )
  
  validacion_lote <- validar_lote_versiones(ejercicios)
  
  expect_type(validacion_lote, "list")
  expect_true("total_ejercicios" %in% names(validacion_lote))
  expect_true("ejercicios_aprobados" %in% names(validacion_lote))
  expect_true("tasa_aprobacion" %in% names(validacion_lote))
  expect_true("calidad_promedio" %in% names(validacion_lote))
  
  expect_equal(validacion_lote$total_ejercicios, 5)
  expect_true(validacion_lote$tasa_aprobacion >= 0 && validacion_lote$tasa_aprobacion <= 1)
})

test_that("validar_lote_versiones maneja lista vacía", {
  validacion_vacia <- validar_lote_versiones(list())
  
  expect_true("error" %in% names(validacion_vacia))
  expect_true(grepl("vacía", validacion_vacia$error))
})

test_that("detectar_tipo_ejercicio funciona correctamente", {
  # Test para movimiento lineal
  datos_movimiento <- list(velocidad = 30, tiempo = 2, distancia = 60)
  tipo1 <- detectar_tipo_ejercicio(datos_movimiento)
  expect_equal(tipo1, "movimiento_lineal")
  
  # Test para estadística
  datos_stats <- list(media = 50, desviacion = 10, muestra = c(45, 55, 50))
  tipo2 <- detectar_tipo_ejercicio(datos_stats)
  expect_equal(tipo2, "estadistica_descriptiva")
  
  # Test para datos vacíos
  tipo3 <- detectar_tipo_ejercicio(list())
  expect_equal(tipo3, "desconocido")
  
  # Test para datos genéricos
  datos_genericos <- list(valor = 123, nombre = "test")
  tipo4 <- detectar_tipo_ejercicio(datos_genericos)
  expect_equal(tipo4, "generico")
})

test_that("casos límite y errores", {
  # Test con datos NULL
  expect_error(validar_datos_icfes(NULL)$aprobado, NA)  # No debe dar error
  
  # Test con datos que no son lista
  validacion_string <- validar_datos_icfes("no es una lista")
  expect_false(validacion_string$aprobado)
  
  # Test de validación matemática con datos incompletos
  datos_incompletos <- list(tipo = "movimiento_lineal", velocidad = 30)
  # No debe fallar, solo dar advertencias
  validacion_inc <- validar_matematica(datos_incompletos)
  expect_type(validacion_inc, "list")
})