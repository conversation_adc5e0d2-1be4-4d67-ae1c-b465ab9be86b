# Tests para funciones de generación de datos

test_that("generar_datos funciona para movimiento lineal", {
  datos <- generar_datos("movimiento_lineal")
  
  expect_type(datos, "list")
  expect_true("tipo" %in% names(datos))
  expect_equal(datos$tipo, "movimiento_lineal")
  expect_true("respuesta_correcta" %in% names(datos))
  expect_true("enunciado" %in% names(datos))
  expect_true(is.numeric(datos$respuesta_correcta))
  expect_true(nchar(datos$enunciado) > 10)
})

test_that("generar_datos funciona para estadística descriptiva", {
  datos <- generar_datos("estadistica_descriptiva")
  
  expect_type(datos, "list")
  expect_equal(datos$tipo, "estadistica_descriptiva")
  expect_true("muestra" %in% names(datos))
  expect_true("media" %in% names(datos))
  expect_true(is.numeric(datos$muestra))
  expect_true(length(datos$muestra) >= 15)
})

test_that("generar_datos con parámetros personalizados", {
  parametros <- list(velocidad_min = 50, velocidad_max = 100)
  datos <- generar_datos("movimiento_lineal", parametros)
  
  expect_true(datos$velocidad >= 50)
  expect_true(datos$velocidad <= 100)
})

test_that("generar_datos falla con tipo inválido", {
  expect_error(generar_datos("tipo_inexistente"))
})

test_that("generar_version_unica genera ejercicios únicos", {
  # Generar múltiples versiones
  version1 <- generar_version_unica("movimiento_lineal")
  version2 <- generar_version_unica("movimiento_lineal")
  
  expect_type(version1, "list")
  expect_type(version2, "list")
  
  # Verificar que tienen hashes diferentes (alta probabilidad)
  expect_false(identical(version1$version_hash, version2$version_hash))
})

test_that("ejecutar_generacion_datos genera número correcto", {
  # Generar 5 ejercicios
  versiones <- ejecutar_generacion_datos(
    tipos_ejercicios = "movimiento_lineal",
    num_versiones = 5,
    seed = 123
  )
  
  expect_length(versiones, 5)
  expect_true(all(sapply(versiones, function(x) x$tipo == "movimiento_lineal")))
  
  # Verificar que tienen IDs únicos
  ids <- sapply(versiones, function(x) x$version_id)
  expect_length(unique(ids), 5)
})

test_that("ejecutar_generacion_datos con múltiples tipos", {
  tipos <- c("movimiento_lineal", "estadistica_descriptiva")
  versiones <- ejecutar_generacion_datos(
    tipos_ejercicios = tipos,
    num_versiones = 4,
    seed = 456
  )
  
  expect_length(versiones, 4)
  
  # Verificar que alternan entre tipos
  tipos_generados <- sapply(versiones, function(x) x$tipo)
  expect_true(all(tipos_generados %in% tipos))
})

test_that("generar_movimiento_lineal calcula distancia correctamente", {
  # Test con parámetros específicos
  parametros <- list(velocidad_min = 30, velocidad_max = 30, 
                    tiempo_min = 2, tiempo_max = 2)
  datos <- generar_movimiento_lineal(parametros)
  
  # Verificar cálculo: d = v * t = 30 * 2 = 60
  expect_equal(datos$velocidad, 30)
  expect_equal(datos$tiempo, 2)
  expect_equal(datos$distancia, 60)
  expect_equal(datos$respuesta_correcta, 60)
})

test_that("generar_estadistica_descriptiva calcula media correctamente", {
  # Mockear datos para test determinístico
  set.seed(789)
  datos <- generar_estadistica_descriptiva()
  
  # Verificar que la media calculada es correcta
  media_manual <- mean(datos$muestra)
  expect_equal(datos$media, round(media_manual, 1))
  expect_equal(datos$respuesta_correcta, round(media_manual, 1))
})

test_that("tests de integración", {
  # Test de flujo completo
  versiones <- ejecutar_generacion_datos(
    tipos_ejercicios = c("movimiento_lineal", "estadistica_descriptiva"),
    num_versiones = 3,
    seed = 999
  )
  
  # Verificar que todos los ejercicios son válidos
  for (ejercicio in versiones) {
    expect_true("tipo" %in% names(ejercicio))
    expect_true("respuesta_correcta" %in% names(ejercicio))
    expect_true("enunciado" %in% names(ejercicio))
    expect_true("version_hash" %in% names(ejercicio))
    expect_true(is.numeric(ejercicio$respuesta_correcta))
    expect_true(nchar(ejercicio$enunciado) > 5)
  }
})