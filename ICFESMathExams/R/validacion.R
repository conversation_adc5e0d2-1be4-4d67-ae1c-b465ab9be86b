# Archivo R restante - validacion.R
#' @title Funciones de Validación para Ejercicios ICFES
#' @description Sistema completo de validación multi-dimensional para ejercicios generados

#' Validar datos ICFES
#' 
#' Función principal de validación que ejecuta todas las validaciones disponibles
#' 
#' @param datos Lista con datos del ejercicio a validar
#' @return Lista con resultados de todas las validaciones
#' @export
#' @examples
#' datos <- generar_datos("movimiento_lineal")
#' validacion <- validar_datos_icfes(datos)
validar_datos_icfes <- function(datos) {
  if (is.null(datos) || length(datos) == 0) {
    return(list(
      aprobado = FALSE,
      error = "Datos vacíos o nulos",
      calidad_total = 0
    ))
  }
  
  # Ejecutar todas las validaciones
  val_matematica <- validar_matematica(datos)
  val_pedagogica <- validar_pedagogica(datos)
  val_tecnica <- validar_tecnica(datos)
  val_contextual <- validar_contextual(datos)
  
  # Calcular puntuación total
  puntuaciones <- c(
    val_matematica$puntuacion,
    val_pedagogica$puntuacion, 
    val_tecnica$puntuacion,
    val_contextual$puntuacion
  )
  
  calidad_total <- mean(puntuaciones, na.rm = TRUE)
  aprobado <- calidad_total >= 0.7 && all(puntuaciones >= 0.5)
  
  list(
    aprobado = aprobado,
    calidad_total = calidad_total,
    matematica = val_matematica,
    pedagogica = val_pedagogica,
    tecnica = val_tecnica,
    contextual = val_contextual,
    resumen = list(
      puntuacion_promedio = calidad_total,
      aspectos_aprobados = sum(puntuaciones >= 0.7),
      aspectos_totales = length(puntuaciones),
      recomendacion = if(aprobado) "Aceptar" else "Revisar"
    ),
    timestamp = Sys.time()
  )
}

#' Validar aspectos matemáticos
#' 
#' Valida la consistencia y corrección matemática del ejercicio
#' 
#' @param datos Lista con datos del ejercicio
#' @return Lista con resultado de validación matemática
#' @export
validar_matematica <- function(datos) {
  errores <- c()
  advertencias <- c()
  puntuacion <- 1.0
  
  # Validar existencia de respuesta correcta
  if (is.null(datos$respuesta_correcta)) {
    errores <- c(errores, "Falta respuesta correcta")
    puntuacion <- puntuacion - 0.3
  }
  
  # Validar coherencia matemática por tipo
  if (!is.null(datos$tipo)) {
    if (datos$tipo == "movimiento_lineal") {
      if (!is.null(datos$velocidad) && !is.null(datos$tiempo)) {
        distancia_calculada <- datos$velocidad * datos$tiempo
        diferencia <- abs(distancia_calculada - datos$respuesta_correcta)
        
        if (diferencia > 0.01) {
          errores <- c(errores, "Inconsistencia en cálculo de distancia")
          puntuacion <- puntuacion - 0.4
        }
      }
    } else if (datos$tipo == "estadistica_descriptiva") {
      if (!is.null(datos$muestra)) {
        media_calculada <- mean(datos$muestra)
        diferencia <- abs(media_calculada - datos$respuesta_correcta)
        
        if (diferencia > 0.1) {
          errores <- c(errores, "Inconsistencia en cálculo de media")
          puntuacion <- puntuacion - 0.4
        }
      }
    }
  }
  
  # Validar rangos numéricos razonables
  if (!is.null(datos$respuesta_correcta)) {
    if (!is.numeric(datos$respuesta_correcta) || !is.finite(datos$respuesta_correcta)) {
      errores <- c(errores, "Respuesta correcta no es un número válido")
      puntuacion <- puntuacion - 0.3
    }
  }
  
  list(
    valido = length(errores) == 0,
    puntuacion = max(0, puntuacion),
    errores = errores,
    advertencias = advertencias,
    detalles = "Validación de consistencia matemática"
  )
}

#' Validar aspectos pedagógicos
#' 
#' Valida la apropiación pedagógica y nivel de dificultad
#' 
#' @param datos Lista con datos del ejercicio
#' @return Lista con resultado de validación pedagógica
#' @export
validar_pedagogica <- function(datos) {
  errores <- c()
  advertencias <- c()
  puntuacion <- 1.0
  
  # Validar existencia de enunciado
  if (is.null(datos$enunciado) || nchar(datos$enunciado) < 10) {
    errores <- c(errores, "Enunciado ausente o muy corto")
    puntuacion <- puntuacion - 0.4
  }
  
  # Validar claridad del enunciado
  if (!is.null(datos$enunciado)) {
    # Verificar que el enunciado termine con pregunta
    if (!grepl("\\?", datos$enunciado)) {
      advertencias <- c(advertencias, "Enunciado no contiene pregunta explícita")
      puntuacion <- puntuacion - 0.1
    }
    
    # Verificar longitud apropiada
    if (nchar(datos$enunciado) > 500) {
      advertencias <- c(advertencias, "Enunciado muy largo")
      puntuacion <- puntuacion - 0.1
    }
  }
  
  # Validar nivel de dificultad apropiado
  if (!is.null(datos$dificultad)) {
    if (!datos$dificultad %in% c("facil", "intermedio", "dificil", "avanzado")) {
      advertencias <- c(advertencias, "Nivel de dificultad no estándar")
      puntuacion <- puntuacion - 0.1
    }
  }
  
  # Validar presencia de contexto
  if (is.null(datos$contexto) || nchar(datos$contexto) < 5) {
    advertencias <- c(advertencias, "Falta contextualización")
    puntuacion <- puntuacion - 0.2
  }
  
  list(
    valido = length(errores) == 0,
    puntuacion = max(0, puntuacion),
    errores = errores,
    advertencias = advertencias,
    detalles = "Validación de aspectos pedagógicos"
  )
}

#' Validar aspectos técnicos
#' 
#' Valida la implementación técnica y estructura de datos
#' 
#' @param datos Lista con datos del ejercicio
#' @return Lista con resultado de validación técnica
#' @export
validar_tecnica <- function(datos) {
  errores <- c()
  advertencias <- c()
  puntuacion <- 1.0
  
  # Validar estructura básica
  campos_requeridos <- c("tipo", "respuesta_correcta", "enunciado")
  campos_faltantes <- setdiff(campos_requeridos, names(datos))
  
  if (length(campos_faltantes) > 0) {
    errores <- c(errores, paste("Campos faltantes:", paste(campos_faltantes, collapse = ", ")))
    puntuacion <- puntuacion - (0.2 * length(campos_faltantes))
  }
  
  # Validar tipos de datos
  if (!is.null(datos$tipo) && !is.character(datos$tipo)) {
    errores <- c(errores, "Tipo debe ser character")
    puntuacion <- puntuacion - 0.2
  }
  
  # Validar hash de versión
  if (is.null(datos$version_hash)) {
    advertencias <- c(advertencias, "Falta hash de versión")
    puntuacion <- puntuacion - 0.1
  }
  
  # Validar timestamp
  if (is.null(datos$timestamp_generacion)) {
    advertencias <- c(advertencias, "Falta timestamp de generación")
    puntuacion <- puntuacion - 0.1
  }
  
  list(
    valido = length(errores) == 0,
    puntuacion = max(0, puntuacion),
    errores = errores,
    advertencias = advertencias,
    detalles = "Validación de implementación técnica"
  )
}

#' Validar aspectos contextuales
#' 
#' Valida la relevancia contextual para estudiantes colombianos
#' 
#' @param datos Lista con datos del ejercicio
#' @return Lista con resultado de validación contextual
#' @export
validar_contextual <- function(datos) {
  errores <- c()
  advertencias <- c()
  puntuacion <- 1.0
  
  # Validar presencia de contexto colombiano
  contexto_texto <- paste(datos$enunciado, datos$contexto, collapse = " ")
  
  # Buscar referencias colombianas
  referencias_colombianas <- c("colombia", "colombiano", "bogotá", "medellín", 
                              "carretera", "estudiante", "icfes", "saber")
  
  tiene_contexto <- any(sapply(referencias_colombianas, function(ref) {
    grepl(ref, tolower(contexto_texto))
  }))
  
  if (!tiene_contexto) {
    advertencias <- c(advertencias, "Falta contextualización colombiana específica")
    puntuacion <- puntuacion - 0.3
  }
  
  # Validar relevancia cultural
  if (!is.null(datos$contexto)) {
    if (grepl("laboratorio|universidad|investigación", tolower(datos$contexto))) {
      puntuacion <- puntuacion + 0.1  # Bonus por contexto académico
    }
  }
  
  # Validar unidades métricas (sistema internacional)
  if (!is.null(datos$unidades_respuesta)) {
    unidades_si <- c("m", "s", "kg", "m/s", "km/h")
    if (any(sapply(unidades_si, function(u) grepl(u, datos$unidades_respuesta)))) {
      puntuacion <- puntuacion + 0.05  # Bonus por usar SI
    }
  }
  
  list(
    valido = length(errores) == 0,
    puntuacion = min(1.0, max(0, puntuacion)),
    errores = errores,
    advertencias = advertencias,
    detalles = "Validación de relevancia contextual"
  )
}

#' Validar lote de versiones
#' 
#' Valida múltiples ejercicios simultáneamente y proporciona estadísticas
#' 
#' @param lista_ejercicios Lista de ejercicios a validar
#' @return Lista con estadísticas de validación del lote
#' @export
validar_lote_versiones <- function(lista_ejercicios) {
  if (length(lista_ejercicios) == 0) {
    return(list(error = "Lista de ejercicios vacía"))
  }
  
  # Validar cada ejercicio
  resultados <- lapply(lista_ejercicios, validar_datos_icfes)
  
  # Calcular estadísticas
  aprobados <- sum(sapply(resultados, function(r) r$aprobado))
  calidades <- sapply(resultados, function(r) r$calidad_total)
  
  # Detectar problemas comunes
  errores_comunes <- obtener_errores_comunes(resultados)
  advertencias_comunes <- obtener_advertencias_comunes(resultados)
  
  list(
    total_ejercicios = length(lista_ejercicios),
    ejercicios_aprobados = aprobados,
    tasa_aprobacion = aprobados / length(lista_ejercicios),
    calidad_promedio = mean(calidades, na.rm = TRUE),
    calidad_minima = min(calidades, na.rm = TRUE),
    calidad_maxima = max(calidades, na.rm = TRUE),
    errores_comunes = errores_comunes,
    advertencias_comunes = advertencias_comunes,
    recomendacion_lote = if(aprobados/length(lista_ejercicios) >= 0.8) {
      "Lote aprobado para uso"
    } else {
      "Revisar ejercicios con baja calidad"
    },
    timestamp = Sys.time()
  )
}

#' Obtener errores comunes
#' 
#' Analiza resultados de validación para identificar errores frecuentes
#' 
#' @param resultados_validacion Lista de resultados de validación
#' @return Vector con errores más comunes
#' @export
obtener_errores_comunes <- function(resultados_validacion) {
  todos_errores <- unlist(lapply(resultados_validacion, function(r) {
    c(r$matematica$errores, r$pedagogica$errores, 
      r$tecnica$errores, r$contextual$errores)
  }))
  
  if (length(todos_errores) == 0) {
    return(character(0))
  }
  
  tabla_errores <- sort(table(todos_errores), decreasing = TRUE)
  names(tabla_errores)[tabla_errores >= 2]  # Errores que aparecen 2+ veces
}

#' Obtener advertencias comunes
#' 
#' Analiza resultados de validación para identificar advertencias frecuentes
#' 
#' @param resultados_validacion Lista de resultados de validación
#' @return Vector con advertencias más comunes
#' @export
obtener_advertencias_comunes <- function(resultados_validacion) {
  todas_advertencias <- unlist(lapply(resultados_validacion, function(r) {
    c(r$matematica$advertencias, r$pedagogica$advertencias,
      r$tecnica$advertencias, r$contextual$advertencias)
  }))
  
  if (length(todas_advertencias) == 0) {
    return(character(0))
  }
  
  tabla_advertencias <- sort(table(todas_advertencias), decreasing = TRUE)
  names(tabla_advertencias)[tabla_advertencias >= 2]  # Advertencias que aparecen 2+ veces
}