#!/usr/bin/env Rscript

#' Script de Construcción Automatizada del Paquete ICFESMathExams
#' 
#' Este script automatiza todo el proceso de construcción, verificación e 
#' instalación del paquete R ICFESMathExams.
#' 
#' Uso:
#'   Rscript build.R
#'   # o desde R:
#'   source("build.R")

cat("=== Construcción del Paquete ICFESMathExams ===\n")
cat("Fecha:", format(Sys.time(), "%Y-%m-%d %H:%M:%S"), "\n\n")

# Verificar directorio
if (!file.exists("DESCRIPTION")) {
  stop("❌ No se encontró DESCRIPTION. ¿Estás en el directorio correcto del paquete?")
}

# Verificar e instalar dependencias necesarias
required_packages <- c("devtools", "roxygen2", "testthat", "knitr", "rmarkdown")
missing_packages <- required_packages[!sapply(required_packages, requireNamespace, quietly = TRUE)]

if (length(missing_packages) > 0) {
  cat("📦 Instalando paquetes faltantes:", paste(missing_packages, collapse = ", "), "\n")
  install.packages(missing_packages)
}

# Cargar librerías necesarias
suppressPackageStartupMessages({
  library(devtools)
  library(roxygen2)
  library(testthat)
})

cat("\n1. 📚 Generando documentación Roxygen2...\n")
tryCatch({
  document()
  cat("   ✅ Documentación generada exitosamente\n")
}, error = function(e) {
  cat("   ❌ Error en documentación:", e$message, "\n")
  stop("Falló la generación de documentación")
})

cat("\n2. 🧪 Ejecutando tests unitarios...\n")
tryCatch({
  test_results <- test()
  
  if (any(sapply(test_results, function(x) x$failed > 0))) {
    cat("   ⚠️ Algunos tests fallaron. Revisa los resultados.\n")
    print(test_results)
  } else {
    cat("   ✅ Todos los tests pasaron exitosamente\n")
  }
}, error = function(e) {
  cat("   ❌ Error ejecutando tests:", e$message, "\n")
  cat("   ⚠️ Continuando sin tests...\n")
})

cat("\n3. 🔍 Verificando el paquete...\n")
tryCatch({
  check_results <- check(
    document = FALSE,  # Ya documentamos arriba
    args = "--no-manual"  # Evitar error si no hay LaTeX
  )
  
  if (length(check_results$errors) > 0) {
    cat("   ❌ Errores encontrados en verificación:\n")
    for (error in check_results$errors) {
      cat("     -", error, "\n")
    }
    cat("   ⚠️ Continuando a pesar de errores...\n")
  } else if (length(check_results$warnings) > 0) {
    cat("   ⚠️ Advertencias encontradas:\n")
    for (warning in check_results$warnings) {
      cat("     -", warning, "\n")
    }
    cat("   ✅ Verificación completada con advertencias\n")
  } else {
    cat("   ✅ Verificación exitosa sin errores ni advertencias\n")
  }
}, error = function(e) {
  cat("   ❌ Error en verificación:", e$message, "\n")
  cat("   ⚠️ Continuando sin verificación completa...\n")
})

cat("\n4. 📦 Instalando el paquete...\n")
tryCatch({
  install(upgrade = "never", quiet = TRUE)
  cat("   ✅ Paquete instalado correctamente\n")
}, error = function(e) {
  cat("   ❌ Error en instalación:", e$message, "\n")
  stop("Falló la instalación del paquete")
})

cat("\n5. 🔬 Verificando instalación...\n")
tryCatch({
  # Cargar el paquete para verificar
  library(ICFESMathExams, quietly = TRUE)
  
  # Verificar que las funciones principales están disponibles
  main_functions <- c(
    "generar_datos", "validar_datos_icfes", "ejecutar_generacion_datos",
    "generar_version_unica", "ejecutar_tests_unitarios"
  )
  
  available_functions <- sapply(main_functions, exists, envir = asNamespace("ICFESMathExams"))
  
  if (all(available_functions)) {
    cat("   ✅ Todas las funciones principales disponibles\n")
    cat("   📊 Funciones cargadas:", sum(available_functions), "de", length(main_functions), "\n")
  } else {
    missing_funcs <- main_functions[!available_functions]
    cat("   ⚠️ Funciones faltantes:", paste(missing_funcs, collapse = ", "), "\n")
  }
  
  # Test rápido de funcionalidad
  cat("   🧪 Ejecutando test rápido...\n")
  test_data <- generar_datos(
    tipo = "movimiento_lineal",
    parametros = list(velocidad_min = 10, velocidad_max = 20)
  )
  
  if (!is.null(test_data) && length(test_data) > 0) {
    cat("   ✅ Test de funcionalidad básica exitoso\n")
  } else {
    cat("   ⚠️ Test de funcionalidad básica falló\n")
  }
  
}, error = function(e) {
  cat("   ❌ Error verificando instalación:", e$message, "\n")
})

cat("\n6. 📖 Intentando generar documentación web...\n")
if (requireNamespace("pkgdown", quietly = TRUE)) {
  tryCatch({
    pkgdown::build_site(preview = FALSE)
    cat("   ✅ Documentación web generada en docs/\n")
    cat("   🌐 Abre docs/index.html en tu navegador para ver la documentación\n")
  }, error = function(e) {
    cat("   ❌ Error generando documentación web:", e$message, "\n")
    cat("   💡 Puedes instalar pkgdown con: install.packages('pkgdown')\n")
  })
} else {
  cat("   ⚠️ pkgdown no disponible, omitiendo documentación web\n")
  cat("   💡 Instala pkgdown para generar documentación web: install.packages('pkgdown')\n")
}

# Información del paquete construido
cat("\n" , paste(rep("=", 50), collapse = ""), "\n")
cat("🎉 CONSTRUCCIÓN COMPLETA 🎉\n")
cat(paste(rep("=", 50), collapse = ""), "\n")

# Mostrar información del paquete
desc_info <- read.dcf("DESCRIPTION")
cat("📦 Paquete:", desc_info[,"Package"], "\n")
cat("📋 Versión:", desc_info[,"Version"], "\n")
cat("👤 Autor:", desc_info[,"Author"], "\n")
cat("📝 Título:", desc_info[,"Title"], "\n")

cat("\n🚀 Próximos pasos:\n")
cat("   1. library(ICFESMathExams)  # Cargar el paquete\n")
cat("   2. ?generar_datos          # Ver documentación\n")
cat("   3. vignette('introduccion') # Ver tutorial\n")
cat("   4. test_package('ICFESMathExams')  # Ejecutar todos los tests\n")

if (file.exists("docs/index.html")) {
  cat("   5. Abre docs/index.html    # Ver documentación web\n")
}

cat("\n✨ ¡El paquete ICFESMathExams está listo para usar! ✨\n")