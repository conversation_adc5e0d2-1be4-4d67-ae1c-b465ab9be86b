#!/usr/bin/env Rscript

# clean.R - Script de limpieza para el paquete ICFESMathExams
# Ejecutar: Rscript clean.R

cat("🧹 Iniciando limpieza del paquete ICFESMathExams...\n\n")

# Función para eliminar directorios/archivos si existen
limpiar_si_existe <- function(ruta, descripcion) {
  if (file.exists(ruta)) {
    if (file.info(ruta)$isdir) {
      unlink(ruta, recursive = TRUE)
    } else {
      file.remove(ruta)
    }
    cat("✅ Eliminado:", descripcion, "\n")
  } else {
    cat("ℹ️  No existe:", descripcion, "\n")
  }
}

# Limpiar archivos de construcción
limpiar_si_existe("man/", "Documentación generada (man/)")
limpiar_si_existe("NAMESPACE", "NAMESPACE generado")
limpiar_si_existe("inst/doc/", "Vignettes compiladas (inst/doc/)")

# Limpiar archivos temporales de R
archivos_temp <- list.files(pattern = "^\\.Rhistory$|^\\.RData$|^\\.Ruserdata$", 
                           all.files = TRUE, recursive = TRUE)
if (length(archivos_temp) > 0) {
  file.remove(archivos_temp)
  cat("✅ Eliminados archivos temporales de R\n")
}

# Limpiar archivos de LaTeX (vignettes)
archivos_latex <- list.files("vignettes/", 
                             pattern = "\\.(aux|bbl|blg|fdb_latexmk|fls|log|out|toc)$", 
                             full.names = TRUE)
if (length(archivos_latex) > 0) {
  file.remove(archivos_latex)
  cat("✅ Eliminados archivos temporales de LaTeX\n")
}

# Limpiar directorio de verificación si existe
limpiar_si_existe("*.Rcheck/", "Directorios de verificación R CMD check")

# Limpiar archivos .tar.gz del paquete
archivos_tar <- list.files(pattern = "ICFESMathExams.*\\.tar\\.gz$")
if (length(archivos_tar) > 0) {
  file.remove(archivos_tar)
  cat("✅ Eliminados archivos .tar.gz previos\n")
}

cat("\n🎉 Limpieza completada exitosamente!")
cat("\n📝 El paquete está listo para una nueva construcción limpia.\n")