# Guía de Instalación: ICFESMathExams en Manjaro Plasma 🐧

## 📋 Prerrequisitos y Preparación

### Paso 1: Actualizar el Sistema

```bash
# Actualizar los repositorios y el sistema
sudo pacman -Syu
```

### Paso 2: Instalar R y Dependencias del Sistema

```bash
# Instalar R y herramientas de desarrollo
sudo pacman -S r gcc-fortran blas lapack git curl wget

# Instalar LaTeX para generar documentación (vignettes)
sudo pacman -S texlive-core texlive-latexextra texlive-fontsextra

# Instalar dependencias adicionales para paquetes R
sudo pacman -S libxml2 openssl libcurl-gnutls pkg-config
```

### Paso 3: Verificar Instalación de R

```bash
# Verificar que R está instalado correctamente
R --version

# Debe mostrar algo como: R version 4.x.x (YYYY-MM-DD)
```

## 📦 Instalación del Paquete ICFESMathExams

### Paso 4: Crear Directorio de Trabajo

```bash
# Crear directorio para el proyecto
mkdir -p ~/R/ICFESMathExams
cd ~/R/ICFESMathExams

# Verificar directorio actual
pwd
```

### Paso 5: Descargar y Extraer el Paquete

```bash
# Si ya descargaste ICFESMathExams_v1.0.0.tar.gz, muévelo aquí:
# mv ~/Downloads/ICFESMathExams_v1.0.0.tar.gz ~/R/ICFESMathExams/

# Extraer el paquete
tar -xzf ICFESMathExams_v1.0.0.tar.gz

# Verificar extracción
ls -la ICFESMathExams/
```

### Paso 6: Instalar Dependencias R

Crear script de instalación de dependencias:

```bash
cat > instalar_dependencias.R << 'EOF'
#!/usr/bin/env Rscript

cat("🔧 Instalando dependencias R para ICFESMathExams...\n\n")

# Configurar repositorio CRAN
options(repos = c(CRAN = "https://cloud.r-project.org/"))

# Lista de dependencias requeridas
dependencias <- c(
  "devtools",      # Herramientas de desarrollo
  "testthat",      # Testing unitario
  "roxygen2",      # Documentación
  "knitr",         # Knitting de documentos
  "rmarkdown",     # Markdown R
  "ggplot2",       # Gráficos
  "dplyr",         # Manipulación de datos
  "readr",         # Lectura de datos
  "stringr",       # Manipulación de strings
  "purrr",         # Programación funcional
  "tibble",        # Data frames modernos
  "magrittr",      # Pipe operator
  "jsonlite",      # JSON
  "yaml",          # YAML
  "DT"             # Tablas interactivas
)

# Función para instalar paquetes
instalar_paquete <- function(paquete) {
  if (!require(paquete, character.only = TRUE, quietly = TRUE)) {
    cat("📦 Instalando", paquete, "...\n")
    install.packages(paquete, dependencies = TRUE, quiet = TRUE)
    if (require(paquete, character.only = TRUE, quietly = TRUE)) {
      cat("✅", paquete, "instalado exitosamente\n")
    } else {
      cat("❌ Error instalando", paquete, "\n")
      return(FALSE)
    }
  } else {
    cat("✅", paquete, "ya está instalado\n")
  }
  return(TRUE)
}

# Instalar todas las dependencias
errores <- 0
for (dep in dependencias) {
  if (!instalar_paquete(dep)) {
    errores <- errores + 1
  }
}

# Resumen
if (errores == 0) {
  cat("\n🎉 ¡Todas las dependencias instaladas exitosamente!\n")
  cat("✨ Listo para instalar ICFESMathExams\n")
} else {
  cat("\n⚠️  Se encontraron", errores, "errores en la instalación\n")
  cat("🔧 Revisa los mensajes anteriores para más detalles\n")
}
EOF

# Hacer ejecutable y ejecutar
chmod +x instalar_dependencias.R
Rscript instalar_dependencias.R
```

### Paso 7: Construir e Instalar ICFESMathExams

```bash
# Navegar al directorio del paquete
cd ICFESMathExams

# Verificar estructura del paquete
Rscript verify.R

# Construir e instalar el paquete
Rscript build.R
```

Si necesitas hacerlo manualmente:

```bash
# Método alternativo manual
cd ~/R/ICFESMathExams

# Instalar usando devtools desde R
R -e "
devtools::install('ICFESMathExams', 
                  dependencies = TRUE, 
                  build_vignettes = TRUE,
                  upgrade = 'never')
"
```

## 🧪 Verificación de la Instalación

### Paso 8: Probar el Paquete

Crear script de prueba:

```bash
cd ~/R/ICFESMathExams
cat > probar_paquete.R << 'EOF'
#!/usr/bin/env Rscript

cat("🧪 Probando instalación de ICFESMathExams...\n\n")

# Intentar cargar el paquete
tryCatch({
  library(ICFESMathExams)
  cat("✅ Paquete cargado exitosamente\n")
}, error = function(e) {
  cat("❌ Error cargando el paquete:", e$message, "\n")
  quit(status = 1)
})

# Verificar funciones principales
funciones_principales <- c(
  "crear_configuracion_basica",
  "generar_datos_icfes", 
  "validar_datos_icfes",
  "exportar_resultados"
)

for (func in funciones_principales) {
  if (exists(func)) {
    cat("✅ Función", func, "disponible\n")
  } else {
    cat("❌ Función", func, "NO encontrada\n")
  }
}

# Prueba básica de funcionalidad
cat("\n🔍 Ejecutando prueba básica...\n")
tryCatch({
  # Crear configuración simple
  config <- crear_configuracion_basica(
    nivel = "grado_11",
    areas = c("algebra"),
    num_ejercicios = 5,
    num_versiones = 2
  )
  cat("✅ Configuración creada exitosamente\n")
  
  # Ver información del paquete
  cat("\n📊 Información del paquete:\n")
  info <- packageDescription("ICFESMathExams")
  cat("Versión:", info$Version, "\n")
  cat("Autor:", info$Author, "\n")
  cat("Descripción:", substr(info$Description, 1, 100), "...\n")
  
}, error = function(e) {
  cat("❌ Error en prueba básica:", e$message, "\n")
  quit(status = 1)
})

cat("\n🎉 ¡ICFESMathExams instalado y funcionando correctamente!\n")
cat("📚 Para empezar, ejecuta: browseVignettes('ICFESMathExams')\n")
EOF

chmod +x probar_paquete.R
Rscript probar_paquete.R
```

### Paso 9: Acceder a la Documentación

```bash
# Ver la documentación en R
R -e "browseVignettes('ICFESMathExams')"

# O ver ayuda específica
R -e "help(package = 'ICFESMathExams')"
```

## 🚀 Primeros Pasos con el Paquete

### Ejemplo Básico de Uso

```bash
cat > ejemplo_basico.R << 'EOF'
#!/usr/bin/env Rscript

# Cargar el paquete
library(ICFESMathExams)

cat("🎯 Ejemplo básico de ICFESMathExams\n\n")

# 1. Crear configuración
config <- crear_configuracion_basica(
  nivel = "grado_11",
  areas = c("algebra", "geometria"),
  num_ejercicios = 10,
  num_versiones = 5
)

cat("✅ Configuración creada:\n")
print(config)

# 2. Generar datos del examen
cat("\n📊 Generando datos del examen...\n")
datos_examen <- generar_datos_icfes(config)
cat("✅ Datos generados exitosamente\n")

# 3. Validar datos
cat("\n🔍 Validando datos...\n")
validacion <- validar_datos_icfes(datos_examen)
cat("✅ Validación completada\n")

# 4. Mostrar resumen
cat("\n📋 Resumen del examen generado:\n")
resumen <- resumen_datos(datos_examen)
print(resumen)

cat("\n🎉 ¡Primer examen generado exitosamente!\n")
EOF

chmod +x ejemplo_basico.R
Rscript ejemplo_basico.R
```

## 🔧 Troubleshooting Común

### Problema 1: Error de Dependencias

```bash
# Si faltan dependencias del sistema:
sudo pacman -S base-devel r gcc-fortran

# Si faltan paquetes R específicos:
R -e "install.packages(c('devtools', 'testthat', 'roxygen2'), dependencies = TRUE)"
```

### Problema 2: Error de Permisos

```bash
# Asegurar permisos correctos
chmod -R 755 ~/R/ICFESMathExams/
chown -R $USER:$USER ~/R/ICFESMathExams/
```

### Problema 3: Error de LaTeX

```bash
# Instalar LaTeX completo si hay problemas con vignettes
sudo pacman -S texlive-most

# O instalar solo lo esencial
sudo pacman -S texlive-core texlive-bin texlive-latexextra
```

### Problema 4: Error de Compilación

```bash
# Limpiar y reinstalar
cd ~/R/ICFESMathExams/ICFESMathExams
Rscript clean.R
Rscript build.R

# O forzar reinstalación
R -e "remove.packages('ICFESMathExams'); devtools::install('~/R/ICFESMathExams/ICFESMathExams')"
```

## 📊 Script de Instalación Automatizada

Para automatizar todo el proceso:

```bash
cat > instalacion_automatica.sh << 'EOF'
#!/bin/bash

echo "🚀 Instalación Automatizada de ICFESMathExams en Manjaro Plasma"
echo "=============================================================="

# Actualizar sistema
echo "📦 Actualizando sistema..."
sudo pacman -Syu --noconfirm

# Instalar dependencias del sistema
echo "🔧 Instalando dependencias del sistema..."
sudo pacman -S --noconfirm r gcc-fortran blas lapack git curl wget \
    texlive-core texlive-latexextra libxml2 openssl pkg-config

# Crear directorio
echo "📁 Creando directorio de trabajo..."
mkdir -p ~/R/ICFESMathExams
cd ~/R/ICFESMathExams

# Instalar dependencias R
echo "📚 Instalando dependencias R..."
Rscript -e "
options(repos = c(CRAN = 'https://cloud.r-project.org/'))
install.packages(c('devtools', 'testthat', 'roxygen2', 'knitr', 'rmarkdown'), dependencies = TRUE)
"

# Extraer paquete (asumiendo que ya está aquí)
if [ -f "ICFESMathExams_v1.0.0.tar.gz" ]; then
    echo "📦 Extrayendo paquete..."
    tar -xzf ICFESMathExams_v1.0.0.tar.gz
    
    # Instalar paquete
    echo "⚙️  Instalando ICFESMathExams..."
    cd ICFESMathExams
    Rscript build.R
    
    # Probar instalación
    echo "🧪 Probando instalación..."
    Rscript -e "library(ICFESMathExams); cat('✅ ICFESMathExams instalado correctamente!\n')"
    
    echo "🎉 ¡Instalación completada exitosamente!"
else
    echo "❌ No se encontró ICFESMathExams_v1.0.0.tar.gz"
    echo "   Coloca el archivo en ~/R/ICFESMathExams/ y ejecuta nuevamente"
fi
EOF

chmod +x instalacion_automatica.sh
```

## 🎯 Comandos de Verificación Final

```bash
# Verificar que todo funciona
R -e "
library(ICFESMathExams)
packageVersion('ICFESMathExams')
help(package = 'ICFESMathExams')
"

# Ver vignettes disponibles
R -e "vignette(package = 'ICFESMathExams')"

# Ejecutar tests del paquete
R -e "devtools::test('~/R/ICFESMathExams/ICFESMathExams')"
```

---

## 📞 Soporte

Si encuentras algún problema:

1. **Ejecuta el diagnóstico**: `Rscript diagnostico.R`
2. **Revisa los logs**: Busca errores específicos en la salida
3. **Verifica dependencias**: `pacman -Q | grep -E 'r |gcc|texlive'`
4. **Limpia y reinstala**: Usa `clean.R` y `build.R`

**¡Tu paquete ICFESMathExams estará listo para transformar la evaluación educativa!** 🚀