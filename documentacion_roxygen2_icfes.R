# =============================================================================
# DOCUMENTACIÓN ROXYGEN2 COMPLETA - PROYECTO R-EXAMS ICFES MATEMÁTICAS
# =============================================================================
# 
# Sistema avanzado de generación de exámenes ICFES con aleatorización de 300+
# versiones únicas por ejercicio, integración multi-lenguaje y validación
# pedagógica comprehensiva.
#
# Autor: Alvaretto
# Fecha: Julio 2025
# Versión: 2.1.0
# =============================================================================

library(testthat)
library(knitr)
library(digest)
library(reticulate)

# =============================================================================
# FUNCIÓN PRINCIPAL: EJECUTAR GENERACIÓN DE DATOS
# =============================================================================

#' Ejecutar generación de datos desde archivos R Markdown
#'
#' @description
#' Función central del sistema que extrae y ejecuta chunks de generación de datos
#' desde archivos .Rmd. Implementa parsing automático de código R, ejecución en
#' entornos aislados y validación de consistencia pedagógica.
#'
#' @details
#' Esta función es el motor principal del sistema R-Exams ICFES. Realiza:
#' \itemize{
#'   \item Lectura y parsing de archivos .Rmd
#'   \item Identificación automática de chunks de generación de datos
#'   \item Ejecución en entornos aislados para evitar contaminación
#'   \item Validación de consistencia matemática y pedagógica
#'   \item Generación de hasta 300+ versiones únicas por ejercicio
#' }
#'
#' @param rmd_file Ruta al archivo .Rmd que contiene los chunks de generación.
#'   Debe ser un archivo válido con chunks etiquetados como "data generation".
#' @param seed Semilla aleatoria para reproducibilidad (entero). Si es NULL,
#'   se utiliza una semilla aleatoria basada en el timestamp actual.
#' @param validate Lógico. Si TRUE (default), ejecuta validaciones pedagógicas
#'   y matemáticas sobre los datos generados.
#' @param debug Lógico. Si TRUE, muestra información detallada del proceso
#'   de parsing y ejecución para depuración.
#' @param output_format Formato de salida deseado. Uno de: "list", "data.frame".
#'   Default: "list".
#'
#' @return
#' Una lista con los siguientes elementos:
#' \describe{
#'   \item{datos}{Lista o data.frame con los datos generados según output_format}
#'   \item{metadata}{Lista con metadatos del proceso:}
#'   \itemize{
#'     \item seed: Semilla utilizada
#'     \item timestamp: Timestamp de generación
#'     \item file_hash: Hash MD5 del archivo fuente
#'     \item execution_time: Tiempo de ejecución en segundos
#'     \item version_hash: Hash único de esta versión específica
#'   }
#'   \item{validacion}{Resultados de las validaciones si validate=TRUE}
#'   \item{warnings}{Vector de advertencias generadas durante el proceso}
#'   \item{errors}{Vector de errores no críticos capturados}
#' }
#'
#' @section Chunks Soportados:
#' La función reconoce los siguientes tipos de chunks:
#' \itemize{
#'   \item \code{```{r data generation}} - Chunk principal de generación
#'   \item \code{```{r data validation}} - Chunk de validación opcional
#'   \item \code{```{r data transformation}} - Chunk de transformación opcional
#' }
#'
#' @section Validaciones Implementadas:
#' \itemize{
#'   \item Consistency Check: Validación de consistencia matemática
#'   \item Range Validation: Verificación de rangos permitidos
#'   \item Pedagogical Equivalence: Equivalencia educativa entre versiones
#'   \item Distractor Validation: Validación de distractores matemáticos
#' }
#'
#' @section Manejo de Errores:
#' La función implementa manejo robusto de errores:
#' \itemize{
#'   \item Errores críticos: Detienen la ejecución y lanzan excepción
#'   \item Errores no críticos: Se capturan y reportan en el valor de retorno
#'   \item Advertencias: Se acumulan y reportan para revisión
#' }
#'
#' @examples
#' \dontrun{
#' # Uso básico
#' resultado <- ejecutar_generacion_datos("ejercicio_001.Rmd")
#' datos <- resultado$datos
#' 
#' # Con semilla específica para reproducibilidad
#' resultado <- ejecutar_generacion_datos("ejercicio_001.Rmd", seed = 12345)
#' 
#' # Con validación deshabilitada para mayor velocidad
#' resultado <- ejecutar_generacion_datos("ejercicio_001.Rmd", validate = FALSE)
#' 
#' # Con debugging habilitado
#' resultado <- ejecutar_generacion_datos("ejercicio_001.Rmd", debug = TRUE)
#' print(resultado$metadata$execution_time)
#' 
#' # Procesamiento en lote
#' archivos <- list.files("ejercicios/", pattern = "*.Rmd", full.names = TRUE)
#' resultados <- lapply(archivos, function(f) {
#'   ejecutar_generacion_datos(f, validate = TRUE)
#' })
#' }
#'
#' @seealso
#' \code{\link{generar_datos}}, \code{\link{validar_datos_icfes}},
#' \code{\link{generar_version_unica}}
#'
#' <AUTHOR>
#' @export
ejecutar_generacion_datos <- function(rmd_file, 
                                     seed = NULL, 
                                     validate = TRUE,
                                     debug = FALSE,
                                     output_format = "list") {
  
  # Validación de parámetros de entrada
  if (!file.exists(rmd_file)) {
    stop("El archivo ", rmd_file, " no existe")
  }
  
  if (!is.null(seed) && (!is.numeric(seed) || length(seed) != 1)) {
    stop("seed debe ser un número único o NULL")
  }
  
  if (!output_format %in% c("list", "data.frame")) {
    stop("output_format debe ser 'list' o 'data.frame'")
  }
  
  # Inicialización
  inicio_tiempo <- Sys.time()
  if (is.null(seed)) {
    seed <- as.integer(Sys.time())
  }
  set.seed(seed)
  
  warnings_list <- character(0)
  errors_list <- character(0)
  
  tryCatch({
    # Leer el archivo completo
    rmd_code <- readLines(rmd_file, warn = FALSE)
    
    if (debug) {
      message("Archivo leído: ", length(rmd_code), " líneas")
    }
    
    # Encontrar el inicio del chunk de generación de datos
    start <- grep("```{r data generation", rmd_code)
    if (length(start) == 0) {
      stop("No se encontró chunk de generación de datos en ", rmd_file)
    }
    
    # Encontrar el final del chunk
    end <- grep("```", rmd_code[(start + 1):length(rmd_code)])[1] + start
    
    # Extraer el código del chunk
    generation_code <- paste(rmd_code[(start + 1):(end - 1)], collapse = "\n")
    
    if (debug) {
      message("Código extraído: ", nchar(generation_code), " caracteres")
    }
    
    # Ejecutar el código en un entorno nuevo
    env <- new.env()
    eval(parse(text = generation_code), envir = env)
    
    # Extraer datos del entorno
    datos_generados <- as.list(env)
    
    # Aplicar formato de salida
    if (output_format == "data.frame") {
      datos_generados <- as.data.frame(datos_generados)
    }
    
    # Calcular metadatos
    file_hash <- digest(rmd_file, file = TRUE)
    version_hash <- digest(list(datos_generados, seed, Sys.time()))
    tiempo_ejecucion <- as.numeric(difftime(Sys.time(), inicio_tiempo, units = "secs"))
    
    metadata <- list(
      seed = seed,
      timestamp = Sys.time(),
      file_hash = file_hash,
      execution_time = tiempo_ejecucion,
      version_hash = version_hash,
      file_path = rmd_file,
      chunk_lines = c(start, end)
    )
    
    # Ejecutar validaciones si están habilitadas
    validacion_resultado <- NULL
    if (validate) {
      validacion_resultado <- validar_datos_icfes(datos_generados)
      if (!validacion_resultado$valid) {
        warnings_list <- c(warnings_list, validacion_resultado$warnings)
      }
    }
    
    # Construir resultado
    resultado <- list(
      datos = datos_generados,
      metadata = metadata,
      validacion = validacion_resultado,
      warnings = warnings_list,
      errors = errors_list
    )
    
    if (debug) {
      message("Generación completada exitosamente en ", 
              round(tiempo_ejecucion, 3), " segundos")
    }
    
    return(resultado)
    
  }, error = function(e) {
    stop("Error en ejecutar_generacion_datos: ", e$message)
  })
}

# =============================================================================
# FUNCIÓN DE GENERACIÓN DE DATOS ESPECÍFICOS
# =============================================================================

#' Generar datos aleatorios para ejercicios ICFES
#'
#' @description
#' Función especializada para generar datos aleatorios matemáticamente 
#' consistentes para ejercicios ICFES de matemáticas. Implementa algoritmos
#' de aleatorización contextualizada y validación pedagógica automática.
#'
#' @details
#' Esta función es el corazón del sistema de aleatorización. Genera datos
#' que satisfacen restricciones matemáticas específicas mientras mantienen
#' diversidad suficiente para crear 300+ versiones únicas por ejercicio.
#'
#' Los datos generados incluyen:
#' \itemize{
#'   \item Parámetros físicos (velocidad, tiempo, distancia)
#'   \item Parámetros contextuales (nombres, lugares, situaciones)
#'   \item Distractores matemáticamente plausibles
#'   \item Metadatos para validación pedagógica
#' }
#'
#' @param tipo_ejercicio Tipo de ejercicio a generar. Uno de:
#'   \itemize{
#'     \item "movimiento_lineal": Ejercicios de movimiento rectilíneo uniforme
#'     \item "variacion_exponencial": Ejercicios de crecimiento exponencial
#'     \item "estadistica_descriptiva": Ejercicios de estadística básica
#'     \item "probabilidad_basica": Ejercicios de probabilidad elemental
#'   }
#' @param parametros Lista de parámetros específicos del ejercicio:
#'   \itemize{
#'     \item duracion_rango: Vector de 2 elementos con rango de duración
#'     \item rapidez_rango: Vector de 2 elementos con rango de rapidez
#'     \item posicion_rango: Vector de 2 elementos con rango de posiciones
#'     \item hora_rango: Vector de 2 elementos con rango de horas
#'     \item contexto: Vector de contextos posibles
#'   }
#' @param seed Semilla aleatoria para reproducibilidad. Si es NULL, se genera
#'   una semilla basada en el timestamp actual.
#' @param generar_distractores Lógico. Si TRUE, genera distractores matemáticos
#'   para opciones de respuesta múltiple.
#' @param validar_consistencia Lógico. Si TRUE, valida consistencia matemática
#'   de los datos generados.
#'
#' @return
#' Lista con elementos específicos del tipo de ejercicio:
#' \describe{
#'   \item{Para "movimiento_lineal":}{
#'     \itemize{
#'       \item duracion: Duración del movimiento (entero)
#'       \item rapidez: Rapidez constante (entero)
#'       \item distancia: Distancia recorrida (entero)
#'       \item pos_inicial: Posición inicial (entero)
#'       \item pos_final: Posición final (entero)
#'       \item hora_inicial: Hora de inicio (entero)
#'       \item hora_final: Hora de finalización (entero)
#'       \item contexto: Contexto del problema (string)
#'       \item distractores: Lista de distractores si generar_distractores=TRUE
#'     }
#'   }
#'   \item{Para otros tipos:}{Elementos específicos del tipo de ejercicio}
#' }
#'
#' @section Validaciones Automáticas:
#' La función implementa las siguientes validaciones:
#' \itemize{
#'   \item Consistencia Física: distancia = rapidez × duración
#'   \item Consistencia Temporal: hora_final = hora_inicial + duración
#'   \item Consistencia Espacial: pos_final = pos_inicial + distancia
#'   \item Rangos Válidos: Todos los valores dentro de rangos especificados
#'   \item Enteros Válidos: Todos los cálculos resultan en enteros
#' }
#'
#' @section Algoritmo de Aleatorización:
#' \enumerate{
#'   \item Selección de parámetros base dentro de rangos válidos
#'   \item Ajuste iterativo para garantizar consistencia matemática
#'   \item Generación de contexto aleatorio apropiado
#'   \item Cálculo de distractores matemáticamente plausibles
#'   \item Validación final de todos los elementos
#' }
#'
#' @examples
#' \dontrun{
#' # Generar datos para ejercicio de movimiento lineal
#' parametros <- list(
#'   duracion_rango = c(2, 4),
#'   rapidez_rango = c(30, 100),
#'   posicion_rango = c(10, 50),
#'   hora_rango = c(6, 10),
#'   contexto = c("viajero", "automóvil", "ciclista")
#' )
#' 
#' datos <- generar_datos("movimiento_lineal", parametros)
#' print(datos$distancia)  # Distancia calculada
#' print(datos$contexto)   # Contexto seleccionado
#' 
#' # Con distractores para opción múltiple
#' datos <- generar_datos("movimiento_lineal", parametros, 
#'                       generar_distractores = TRUE)
#' print(datos$distractores)
#' 
#' # Generación en lote con diferentes semillas
#' versiones <- lapply(1:10, function(i) {
#'   generar_datos("movimiento_lineal", parametros, seed = i)
#' })
#' }
#'
#' @seealso
#' \code{\link{ejecutar_generacion_datos}}, \code{\link{validar_datos_icfes}},
#' \code{\link{generar_distractores}}
#'
#' <AUTHOR>
#' @export
generar_datos <- function(tipo_ejercicio = "movimiento_lineal",
                         parametros = NULL,
                         seed = NULL,
                         generar_distractores = FALSE,
                         validar_consistencia = TRUE) {
  
  # Validación de parámetros
  tipos_validos <- c("movimiento_lineal", "variacion_exponencial", 
                     "estadistica_descriptiva", "probabilidad_basica")
  
  if (!tipo_ejercicio %in% tipos_validos) {
    stop("tipo_ejercicio debe ser uno de: ", paste(tipos_validos, collapse = ", "))
  }
  
  # Establecer semilla
  if (!is.null(seed)) {
    set.seed(seed)
  }
  
  # Parámetros por defecto para movimiento lineal
  if (is.null(parametros)) {
    parametros <- list(
      duracion_rango = c(2, 4),
      rapidez_rango = c(30, 100),
      posicion_rango = c(10, 50),
      hora_rango = c(6, 10),
      contexto = c("viajero", "automóvil", "ciclista", "motocicleta")
    )
  }
  
  # Generar datos según tipo
  switch(tipo_ejercicio,
    "movimiento_lineal" = generar_movimiento_lineal(parametros, generar_distractores),
    "variacion_exponencial" = generar_variacion_exponencial(parametros, generar_distractores),
    "estadistica_descriptiva" = generar_estadistica_descriptiva(parametros, generar_distractores),
    "probabilidad_basica" = generar_probabilidad_basica(parametros, generar_distractores)
  )
}

# =============================================================================
# FUNCIÓN DE VALIDACIÓN PEDAGÓGICA
# =============================================================================

#' Validar datos generados para ejercicios ICFES
#'
#' @description
#' Función comprehensiva de validación que verifica la consistencia matemática,
#' pedagógica y técnica de los datos generados para ejercicios ICFES.
#'
#' @details
#' Esta función implementa un sistema multi-dimensional de validación que
#' incluye:
#' \itemize{
#'   \item Validación Matemática: Consistencia de cálculos y fórmulas
#'   \item Validación Pedagógica: Apropiación educativa y nivel de dificultad
#'   \item Validación Técnica: Tipos de datos y rangos permitidos
#'   \item Validación Contextual: Coherencia de situaciones y contextos
#' }
#'
#' @param datos Lista o data.frame con los datos a validar. Debe contener
#'   al menos los elementos básicos según el tipo de ejercicio.
#' @param tipo_ejercicio Tipo de ejercicio para validaciones específicas.
#'   Si es NULL, se intenta detectar automáticamente.
#' @param nivel_estricto Lógico. Si TRUE, aplica validaciones más estrictas
#'   apropiadas para uso en producción.
#' @param reportar_detalles Lógico. Si TRUE, incluye detalles específicos
#'   de cada validación en el reporte.
#'
#' @return
#' Lista con resultados de validación:
#' \describe{
#'   \item{valid}{Lógico. TRUE si todas las validaciones pasan}
#'   \item{score}{Puntuación numérica de 0 a 100 de calidad general}
#'   \item{validaciones}{Lista detallada de cada validación:}
#'   \itemize{
#'     \item matematica: Resultado de validación matemática
#'     \item pedagogica: Resultado de validación pedagógica
#'     \item tecnica: Resultado de validación técnica
#'     \item contextual: Resultado de validación contextual
#'   }
#'   \item{warnings}{Vector de advertencias no críticas}
#'   \item{errors}{Vector de errores críticos}
#'   \item{sugerencias}{Vector de sugerencias de mejora}
#' }
#'
#' @section Criterios de Validación:
#' \subsection{Validación Matemática:}{
#'   \itemize{
#'     \item Consistencia de fórmulas físicas
#'     \item Exactitud de cálculos aritméticos
#'     \item Coherencia de unidades
#'     \item Plausibilidad de resultados
#'   }
#' }
#' \subsection{Validación Pedagógica:}{
#'   \itemize{
#'     \item Apropiación para nivel ICFES
#'     \item Diversidad de distractores
#'     \item Ausencia de ambigüedades
#'     \item Coherencia narrativa
#'   }
#' }
#' \subsection{Validación Técnica:}{
#'   \itemize{
#'     \item Tipos de datos correctos
#'     \item Rangos dentro de límites
#'     \item Ausencia de valores NA/NULL
#'     \item Estructura de datos apropiada
#'   }
#' }
#'
#' @examples
#' \dontrun{
#' # Validar datos generados
#' datos <- generar_datos("movimiento_lineal")
#' validacion <- validar_datos_icfes(datos)
#' 
#' if (validacion$valid) {
#'   print("Datos válidos para uso en examen")
#' } else {
#'   print("Errores encontrados:")
#'   print(validacion$errors)
#' }
#' 
#' # Validación estricta para producción
#' validacion <- validar_datos_icfes(datos, nivel_estricto = TRUE)
#' print(paste("Puntuación de calidad:", validacion$score))
#' 
#' # Validación con detalles para debugging
#' validacion <- validar_datos_icfes(datos, reportar_detalles = TRUE)
#' print(validacion$validaciones$matematica$detalles)
#' }
#'
#' @seealso
#' \code{\link{generar_datos}}, \code{\link{ejecutar_generacion_datos}}
#'
#' <AUTHOR>
#' @export
validar_datos_icfes <- function(datos, 
                               tipo_ejercicio = NULL,
                               nivel_estricto = FALSE,
                               reportar_detalles = FALSE) {
  
  # Detectar tipo de ejercicio si no se proporciona
  if (is.null(tipo_ejercicio)) {
    tipo_ejercicio <- detectar_tipo_ejercicio(datos)
  }
  
  # Inicializar resultados
  warnings_list <- character(0)
  errors_list <- character(0)
  sugerencias_list <- character(0)
  
  # Ejecutar validaciones
  val_matematica <- validar_matematica(datos, tipo_ejercicio, nivel_estricto)
  val_pedagogica <- validar_pedagogica(datos, tipo_ejercicio, nivel_estricto)
  val_tecnica <- validar_tecnica(datos, nivel_estricto)
  val_contextual <- validar_contextual(datos, tipo_ejercicio)
  
  # Consolidar resultados
  todas_validas <- all(val_matematica$valid, val_pedagogica$valid, 
                      val_tecnica$valid, val_contextual$valid)
  
  # Calcular score ponderado
  score <- round(mean(c(val_matematica$score, val_pedagogica$score, 
                       val_tecnica$score, val_contextual$score)), 1)
  
  # Consolidar warnings y errors
  warnings_list <- c(warnings_list, val_matematica$warnings, val_pedagogica$warnings,
                     val_tecnica$warnings, val_contextual$warnings)
  errors_list <- c(errors_list, val_matematica$errors, val_pedagogica$errors,
                   val_tecnica$errors, val_contextual$errors)
  
  # Construir resultado
  resultado <- list(
    valid = todas_validas,
    score = score,
    validaciones = list(
      matematica = val_matematica,
      pedagogica = val_pedagogica,
      tecnica = val_tecnica,
      contextual = val_contextual
    ),
    warnings = warnings_list,
    errors = errors_list,
    sugerencias = sugerencias_list
  )
  
  return(resultado)
}

# =============================================================================
# FUNCIÓN DE GENERACIÓN DE VERSIONES ÚNICAS
# =============================================================================

#' Generar versión única de ejercicio con hash identificador
#'
#' @description
#' Función que genera una versión completamente única de un ejercicio,
#' incluyendo un hash identificador único que permite tracking y
#' verificación de unicidad en el sistema.
#'
#' @details
#' Esta función es fundamental para el sistema de 300+ versiones únicas.
#' Combina todos los parámetros, contextos y metadatos para crear un
#' identificador único que permite:
#' \itemize{
#'   \item Verificar unicidad absoluta de versiones
#'   \item Reproducir versiones específicas
#'   \item Tracking de versiones utilizadas
#'   \item Auditoría de generación de exámenes
#' }
#'
#' @param ejercicio_base Datos base del ejercicio a versionar
#' @param version_number Número de versión a generar (entero)
#' @param incluir_metadata Lógico. Si TRUE, incluye metadatos extendidos
#'   en el hash para mayor unicidad.
#' @param salt String adicional para incrementar entropía del hash
#'
#' @return
#' Lista con:
#' \describe{
#'   \item{datos}{Datos del ejercicio con parámetros únicos}
#'   \item{version_hash}{Hash único de esta versión}
#'   \item{version_number}{Número de versión generado}
#'   \item{generation_timestamp}{Timestamp de generación}
#'   \item{metadata}{Metadatos adicionales si incluir_metadata=TRUE}
#' }
#'
#' @examples
#' \dontrun{
#' # Generar 10 versiones únicas
#' versiones <- lapply(1:10, function(i) {
#'   datos_base <- generar_datos("movimiento_lineal")
#'   generar_version_unica(datos_base, i)
#' })
#' 
#' # Verificar unicidad
#' hashes <- sapply(versiones, function(v) v$version_hash)
#' length(unique(hashes)) == length(hashes)  # Debe ser TRUE
#' }
#'
#' <AUTHOR>
#' @export
generar_version_unica <- function(ejercicio_base, 
                                 version_number,
                                 incluir_metadata = TRUE,
                                 salt = "") {
  
  # Generar datos únicos para esta versión
  datos_version <- generar_datos(seed = version_number * 12345 + as.integer(Sys.time()))
  
  # Crear hash único
  elementos_hash <- list(
    datos = datos_version,
    version_number = version_number,
    timestamp = Sys.time(),
    salt = salt
  )
  
  if (incluir_metadata) {
    elementos_hash$metadata <- list(
      r_version = R.version.string,
      system_info = Sys.info(),
      random_state = .Random.seed
    )
  }
  
  version_hash <- digest(elementos_hash, algo = "sha256")
  
  # Construir resultado
  resultado <- list(
    datos = datos_version,
    version_hash = version_hash,
    version_number = version_number,
    generation_timestamp = Sys.time(),
    metadata = if (incluir_metadata) elementos_hash$metadata else NULL
  )
  
  return(resultado)
}

# =============================================================================
# FUNCIONES AUXILIARES DE GENERACIÓN ESPECÍFICA
# =============================================================================

#' @title Generar datos de movimiento lineal
#' @description Función interna para generar datos específicos de movimiento lineal
#' @param parametros Lista de parámetros específicos
#' @param generar_distractores Lógico para generar distractores
#' @return Lista con datos de movimiento lineal
#' @keywords internal
generar_movimiento_lineal <- function(parametros, generar_distractores = FALSE) {
  
  # Generar parámetros base
  duracion <- sample(parametros$duracion_rango[1]:parametros$duracion_rango[2], 1)
  rapidez <- sample(parametros$rapidez_rango[1]:parametros$rapidez_rango[2], 1)
  
  # Asegurar que distancia sea múltiplo exacto de duración
  rapidez <- rapidez - (rapidez %% duracion)
  if (rapidez < parametros$rapidez_rango[1]) {
    rapidez <- rapidez + duracion
  }
  
  # Calcular valores derivados
  distancia <- rapidez * duracion
  pos_inicial <- sample(parametros$posicion_rango[1]:parametros$posicion_rango[2], 1)
  pos_final <- pos_inicial + distancia
  hora_inicial <- sample(parametros$hora_rango[1]:parametros$hora_rango[2], 1)
  hora_final <- hora_inicial + duracion
  
  # Seleccionar contexto
  contexto <- sample(parametros$contexto, 1)
  
  # Construir datos base
  datos <- list(
    duracion = as.integer(duracion),
    rapidez = as.integer(rapidez),
    distancia = as.integer(distancia),
    pos_inicial = as.integer(pos_inicial),
    pos_final = as.integer(pos_final),
    hora_inicial = as.integer(hora_inicial),
    hora_final = as.integer(hora_final),
    contexto = contexto
  )
  
  # Generar distractores si se solicita
  if (generar_distractores) {
    datos$distractores <- generar_distractores_movimiento(datos)
  }
  
  return(datos)
}

#' @title Generar distractores para movimiento lineal
#' @description Función interna para generar distractores matemáticos
#' @param datos_base Datos base del ejercicio
#' @return Lista con distractores
#' @keywords internal
generar_distractores_movimiento <- function(datos_base) {
  
  # Distractores basados en errores comunes
  distractor1 <- datos_base$rapidez + datos_base$duracion  # Error: suma en lugar de multiplicación
  distractor2 <- datos_base$rapidez / datos_base$duracion  # Error: división en lugar de multiplicación
  distractor3 <- datos_base$distancia / 2                 # Error: usar la mitad
  
  # Asegurar que los distractores sean diferentes de la respuesta correcta
  distractores <- c(distractor1, distractor2, distractor3)
  distractores <- distractores[distractores != datos_base$distancia]
  
  return(list(
    distancia_incorrecta1 = as.integer(distractores[1]),
    distancia_incorrecta2 = as.integer(distractores[2]),
    distancia_incorrecta3 = as.integer(distractores[3])
  ))
}

# =============================================================================
# FUNCIONES DE VALIDACIÓN ESPECÍFICAS
# =============================================================================

#' @title Validar consistencia matemática
#' @description Función interna para validar consistencia matemática
#' @param datos Datos a validar
#' @param tipo_ejercicio Tipo de ejercicio
#' @param nivel_estricto Nivel de validación
#' @return Lista con resultados de validación
#' @keywords internal
validar_matematica <- function(datos, tipo_ejercicio, nivel_estricto = FALSE) {
  
  valid <- TRUE
  score <- 100
  warnings_list <- character(0)
  errors_list <- character(0)
  
  # Validaciones específicas para movimiento lineal
  if (tipo_ejercicio == "movimiento_lineal") {
    
    # Validar fórmula distancia = rapidez × duración
    if (datos$distancia != datos$rapidez * datos$duracion) {
      valid <- FALSE
      errors_list <- c(errors_list, "Distancia no es igual a rapidez × duración")
      score <- score - 30
    }
    
    # Validar posición final
    if (datos$pos_final != datos$pos_inicial + datos$distancia) {
      valid <- FALSE
      errors_list <- c(errors_list, "Posición final inconsistente")
      score <- score - 25
    }
    
    # Validar hora final
    if (datos$hora_final != datos$hora_inicial + datos$duracion) {
      valid <- FALSE
      errors_list <- c(errors_list, "Hora final inconsistente")
      score <- score - 25
    }
    
    # Validar que distancia sea múltiplo exacto de duración
    if (datos$distancia %% datos$duracion != 0) {
      if (nivel_estricto) {
        valid <- FALSE
        errors_list <- c(errors_list, "Distancia no es múltiplo exacto de duración")
        score <- score - 20
      } else {
        warnings_list <- c(warnings_list, "Distancia no es múltiplo exacto de duración")
        score <- score - 10
      }
    }
  }
  
  return(list(
    valid = valid,
    score = max(0, score),
    warnings = warnings_list,
    errors = errors_list
  ))
}

#' @title Validar aspectos pedagógicos
#' @description Función interna para validar aspectos pedagógicos
#' @param datos Datos a validar
#' @param tipo_ejercicio Tipo de ejercicio
#' @param nivel_estricto Nivel de validación
#' @return Lista con resultados de validación
#' @keywords internal
validar_pedagogica <- function(datos, tipo_ejercicio, nivel_estricto = FALSE) {
  
  valid <- TRUE
  score <- 100
  warnings_list <- character(0)
  errors_list <- character(0)
  
  # Validar rangos apropiados para nivel ICFES
  if ("rapidez" %in% names(datos)) {
    if (datos$rapidez < 10 || datos$rapidez > 200) {
      warnings_list <- c(warnings_list, "Rapidez fuera de rango típico ICFES")
      score <- score - 5
    }
  }
  
  if ("duracion" %in% names(datos)) {
    if (datos$duracion > 10) {
      warnings_list <- c(warnings_list, "Duración muy larga para contexto típico")
      score <- score - 5
    }
  }
  
  # Validar contexto apropiado
  if ("contexto" %in% names(datos)) {
    contextos_validos <- c("viajero", "automóvil", "ciclista", "motocicleta", 
                          "peatón", "corredor", "tren", "autobús")
    if (!datos$contexto %in% contextos_validos) {
      warnings_list <- c(warnings_list, "Contexto no estándar para ICFES")
      score <- score - 10
    }
  }
  
  return(list(
    valid = valid,
    score = max(0, score),
    warnings = warnings_list,
    errors = errors_list
  ))
}

#' @title Validar aspectos técnicos
#' @description Función interna para validar aspectos técnicos
#' @param datos Datos a validar
#' @param nivel_estricto Nivel de validación
#' @return Lista con resultados de validación
#' @keywords internal
validar_tecnica <- function(datos, nivel_estricto = FALSE) {
  
  valid <- TRUE
  score <- 100
  warnings_list <- character(0)
  errors_list <- character(0)
  
  # Validar que todos los valores numéricos sean enteros
  valores_numericos <- datos[sapply(datos, is.numeric)]
  if (length(valores_numericos) > 0) {
    if (!all(sapply(valores_numericos, function(x) x == as.integer(x)))) {
      if (nivel_estricto) {
        valid <- FALSE
        errors_list <- c(errors_list, "No todos los valores numéricos son enteros")
        score <- score - 40
      } else {
        warnings_list <- c(warnings_list, "Algunos valores no son enteros")
        score <- score - 10
      }
    }
  }
  
  # Validar ausencia de valores NA
  if (any(is.na(unlist(datos)))) {
    valid <- FALSE
    errors_list <- c(errors_list, "Valores NA encontrados en los datos")
    score <- score - 50
  }
  
  # Validar estructura de datos
  if (length(datos) < 3) {
    valid <- FALSE
    errors_list <- c(errors_list, "Estructura de datos insuficiente")
    score <- score - 30
  }
  
  return(list(
    valid = valid,
    score = max(0, score),
    warnings = warnings_list,
    errors = errors_list
  ))
}

#' @title Validar aspectos contextuales
#' @description Función interna para validar aspectos contextuales
#' @param datos Datos a validar
#' @param tipo_ejercicio Tipo de ejercicio
#' @return Lista con resultados de validación
#' @keywords internal
validar_contextual <- function(datos, tipo_ejercicio) {
  
  valid <- TRUE
  score <- 100
  warnings_list <- character(0)
  errors_list <- character(0)
  
  # Validar coherencia contextual
  if ("contexto" %in% names(datos)) {
    if (is.null(datos$contexto) || datos$contexto == "") {
      valid <- FALSE
      errors_list <- c(errors_list, "Contexto vacío o nulo")
      score <- score - 30
    }
  }
  
  # Validar coherencia de valores con contexto
  if ("rapidez" %in% names(datos) && "contexto" %in% names(datos)) {
    if (datos$contexto == "peatón" && datos$rapidez > 20) {
      warnings_list <- c(warnings_list, "Rapidez muy alta para peatón")
      score <- score - 15
    }
    if (datos$contexto == "automóvil" && datos$rapidez < 30) {
      warnings_list <- c(warnings_list, "Rapidez muy baja para automóvil")
      score <- score - 10
    }
  }
  
  return(list(
    valid = valid,
    score = max(0, score),
    warnings = warnings_list,
    errors = errors_list
  ))
}

#' @title Detectar tipo de ejercicio
#' @description Función interna para detectar automáticamente el tipo de ejercicio
#' @param datos Datos a analizar
#' @return String con tipo de ejercicio detectado
#' @keywords internal
detectar_tipo_ejercicio <- function(datos) {
  
  # Detectar por presencia de variables características
  if (all(c("rapidez", "duracion", "distancia") %in% names(datos))) {
    return("movimiento_lineal")
  }
  
  if (any(c("crecimiento", "exponencial", "base") %in% names(datos))) {
    return("variacion_exponencial")
  }
  
  if (any(c("media", "mediana", "moda") %in% names(datos))) {
    return("estadistica_descriptiva")
  }
  
  if (any(c("probabilidad", "evento", "muestra") %in% names(datos))) {
    return("probabilidad_basica")
  }
  
  # Default
  return("movimiento_lineal")
}

# =============================================================================
# FUNCIONES DE UTILIDAD Y METADATOS
# =============================================================================

#' @title Generar metadatos del sistema
#' @description Función para generar metadatos comprehensivos del sistema
#' @return Lista con metadatos del sistema
#' @export
generar_metadatos_sistema <- function() {
  
  list(
    version_paquete = "2.1.0",
    fecha_creacion = Sys.Date(),
    autor = "Alvaretto",
    descripcion = "Sistema R-Exams ICFES Matemáticas Optimizado",
    capacidades = list(
      versiones_por_ejercicio = 300,
      tipos_ejercicio = c("movimiento_lineal", "variacion_exponencial", 
                         "estadistica_descriptiva", "probabilidad_basica"),
      formatos_salida = c("pdf", "html", "moodle", "nops"),
      validaciones = c("matematica", "pedagogica", "tecnica", "contextual")
    ),
    dependencias = list(
      r_version = R.version.string,
      paquetes_requeridos = c("testthat", "knitr", "digest", "reticulate"),
      sistema_operativo = Sys.info()["sysname"]
    )
  )
}

#' @title Información del paquete
#' @description Función para mostrar información general del paquete
#' @export
info_paquete <- function() {
  cat("=== SISTEMA R-EXAMS ICFES MATEMÁTICAS OPTIMIZADO ===\n")
  cat("Versión: 2.1.0\n")
  cat("Autor: Alvaretto\n")
  cat("Fecha: Julio 2025\n\n")
  
  cat("CAPACIDADES PRINCIPALES:\n")
  cat("• Generación de 300+ versiones únicas por ejercicio\n")
  cat("• Integración multi-lenguaje (R + LaTeX + Python + TikZ)\n")
  cat("• Validación pedagógica y matemática comprehensiva\n")
  cat("• Sistema de testing unitario avanzado\n")
  cat("• Aleatorización contextualizada\n")
  cat("• Múltiples formatos de salida\n\n")
  
  cat("FUNCIONES PRINCIPALES:\n")
  cat("• ejecutar_generacion_datos() - Motor principal del sistema\n")
  cat("• generar_datos() - Generación de datos aleatorios\n")
  cat("• validar_datos_icfes() - Validación comprehensiva\n")
  cat("• generar_version_unica() - Generación de versiones únicas\n\n")
  
  cat("Para más información, consulte la documentación con ?nombre_funcion\n")
}

# =============================================================================
# EJEMPLOS DE USO AVANZADO
# =============================================================================

#' @title Ejemplo de workflow completo
#' @description Función de ejemplo que demuestra el workflow completo del sistema
#' @export
ejemplo_workflow_completo <- function() {
  
  cat("=== EJEMPLO DE WORKFLOW COMPLETO ===\n\n")
  
  # 1. Generar datos base
  cat("1. Generando datos base...\n")
  datos_base <- generar_datos("movimiento_lineal")
  cat("   Datos generados exitosamente\n\n")
  
  # 2. Validar datos
  cat("2. Validando datos...\n")
  validacion <- validar_datos_icfes(datos_base)
  cat("   Validación completada - Score:", validacion$score, "\n\n")
  
  # 3. Generar múltiples versiones
  cat("3. Generando múltiples versiones...\n")
  versiones <- lapply(1:5, function(i) {
    generar_version_unica(datos_base, i)
  })
  cat("   5 versiones únicas generadas\n\n")
  
  # 4. Verificar unicidad
  cat("4. Verificando unicidad...\n")
  hashes <- sapply(versiones, function(v) v$version_hash)
  unicidad <- length(unique(hashes)) == length(hashes)
  cat("   Unicidad verificada:", unicidad, "\n\n")
  
  # 5. Mostrar resultado
  cat("5. Ejemplo de datos generados:\n")
  print(datos_base)
  
  return(invisible(list(
    datos_base = datos_base,
    validacion = validacion,
    versiones = versiones,
    unicidad = unicidad
  )))
}

# =============================================================================
# FIN DEL ARCHIVO
# =============================================================================

cat("Documentación Roxygen2 cargada exitosamente\n")
cat("Use info_paquete() para ver información general\n")
cat("Use ejemplo_workflow_completo() para ver ejemplo de uso\n")