# Troubleshooting ICFESMathExams en Manjaro Plasma 🔧

## 🚨 Problemas Comunes y Soluciones

### Error 1: "R no encontrado" o "command not found: R"

**Síntomas:**
```
bash: R: command not found
```

**Solución:**
```bash
# Instalar R
sudo pacman -S r

# Verificar instalación
R --version

# Si sigue fallando, agregar al PATH
echo 'export PATH="/usr/bin:$PATH"' >> ~/.bashrc
source ~/.bashrc
```

---

### Error 2: "Error en la compilación de paquetes"

**Síntomas:**
```
ERROR: compilation failed for package 'ICFESMathExams'
```

**Soluciones:**

**Paso 1 - Verificar compiladores:**
```bash
# Instalar herramientas de compilación
sudo pacman -S base-devel gcc-fortran

# Verificar
gcc --version
gfortran --version
```

**Paso 2 - Verificar bibliotecas matemáticas:**
```bash
# Instalar BLAS y LAPACK
sudo pacman -S blas lapack

# Verificar en R
R -e "capabilities()"
```

**Paso 3 - Limpiar y reinstalar:**
```bash
cd ~/R/ICFESMathExams/ICFESMathExams
Rscript clean.R
R -e "remove.packages('ICFESMathExams')"
Rscript build.R
```

---

### Error 3: "devtools no disponible"

**Síntomas:**
```
Error in library(devtools) : there is no package called 'devtools'
```

**Solución:**
```bash
# Instalar devtools
R -e "install.packages('devtools', repos='https://cloud.r-project.org/', dependencies=TRUE)"

# Si falla, instalar dependencias del sistema primero
sudo pacman -S libxml2 openssl libcurl-gnutls pkg-config

# Intentar nuevamente
R -e "install.packages('devtools', repos='https://cloud.r-project.org/', dependencies=TRUE)"
```

---

### Error 4: "Error al generar vignettes"

**Síntomas:**
```
Error: processing vignette 'introduccion.Rmd' failed with diagnostics:
LaTeX failed to compile
```

**Soluciones:**

**Opción 1 - LaTeX completo:**
```bash
sudo pacman -S texlive-most
```

**Opción 2 - LaTeX mínimo:**
```bash
sudo pacman -S texlive-core texlive-latexextra texlive-fontsextra
```

**Opción 3 - Instalar sin vignettes:**
```bash
R -e "devtools::install('~/R/ICFESMathExams/ICFESMathExams', build_vignettes=FALSE)"
```

---

### Error 5: "Dependencias faltantes"

**Síntomas:**
```
Error: package 'ggplot2' is not available
```

**Solución automática:**
```bash
# Script para instalar todas las dependencias
R -e "
deps <- c('ggplot2', 'dplyr', 'readr', 'stringr', 'purrr', 'tibble', 'magrittr', 'jsonlite', 'yaml', 'DT', 'testthat', 'roxygen2', 'knitr', 'rmarkdown')
for(pkg in deps) {
  if(!require(pkg, character.only=TRUE, quietly=TRUE)) {
    install.packages(pkg, repos='https://cloud.r-project.org/', dependencies=TRUE)
  }
}
"
```

---

### Error 6: "Permisos insuficientes"

**Síntomas:**
```
Warning: unable to access index for repository
Error: installation of package had non-zero exit status
```

**Soluciones:**
```bash
# Verificar permisos del directorio R
ls -la ~/R/

# Corregir permisos si es necesario
sudo chown -R $USER:$USER ~/R/
chmod -R 755 ~/R/

# Verificar permisos de escritura
touch ~/R/test_write && rm ~/R/test_write
```

---

### Error 7: "Error de red/CRAN"

**Síntomas:**
```
Warning: unable to access index for repository https://cloud.r-project.org/
```

**Soluciones:**
```bash
# Verificar conexión
ping -c 3 cloud.r-project.org

# Usar espejo alternativo
R -e "options(repos = c(CRAN = 'https://cran.rstudio.com/')); install.packages('devtools')"

# O usar espejo local (si existe)
R -e "options(repos = c(CRAN = 'https://cran.r-project.org/')); install.packages('devtools')"
```

---

### Error 8: "Función no encontrada después de instalación"

**Síntomas:**
```
Error: object 'crear_configuracion_basica' not found
```

**Diagnóstico y solución:**
```bash
# Verificar que el paquete está instalado
R -e "installed.packages()['ICFESMathExams',]"

# Verificar que se puede cargar
R -e "library(ICFESMathExams)"

# Ver funciones disponibles
R -e "library(ICFESMathExams); ls('package:ICFESMathExams')"

# Si no aparecen funciones, reinstalar
R -e "remove.packages('ICFESMathExams')"
cd ~/R/ICFESMathExams/ICFESMathExams
Rscript build.R
```

---

### Error 9: "Espacio insuficiente"

**Síntomas:**
```
Error: No space left on device
```

**Soluciones:**
```bash
# Verificar espacio
df -h

# Limpiar cache de pacman
sudo pacman -Sc

# Limpiar archivos temporales R
R -e "unlink(tempdir(), recursive=TRUE)"

# Limpiar archivos de construcción
cd ~/R/ICFESMathExams/ICFESMathExams
Rscript clean.R
```

---

### Error 10: "Tests fallan"

**Síntomas:**
```
Error: Test failures
```

**Diagnóstico:**
```bash
# Ejecutar tests individuales
cd ~/R/ICFESMathExams/ICFESMathExams
R -e "testthat::test_file('tests/testthat/test-generacion-datos.R')"

# Ver detalles del error
R -e "devtools::test(reporter='summary')"

# Verificar que las funciones básicas funcionan
R -e "
library(ICFESMathExams)
config <- crear_configuracion_basica(nivel='grado_11', areas='algebra', num_ejercicios=1, num_versiones=1)
print('Test básico exitoso')
"
```

---

## 🔍 Scripts de Diagnóstico

### Diagnóstico Completo
```bash
cat > diagnostico_completo.R << 'EOF'
cat("🔍 Diagnóstico Completo ICFESMathExams\n")
cat("=====================================\n\n")

# 1. Información del sistema
cat("💻 Sistema:\n")
cat("OS:", Sys.info()["sysname"], Sys.info()["version"], "\n")
cat("R version:", R.version.string, "\n\n")

# 2. Capacidades de R
cat("🔧 Capacidades de R:\n")
caps <- capabilities()
important_caps <- c("X11", "tcltk", "iconv", "NLS", "profmem", "cairo")
for(cap in important_caps) {
  status <- if(caps[cap]) "✅" else "❌"
  cat(sprintf("%s %s\n", status, cap))
}
cat("\n")

# 3. Paquetes requeridos
cat("📦 Estado de dependencias:\n")
required_packages <- c("devtools", "testthat", "roxygen2", "knitr", "rmarkdown")
for(pkg in required_packages) {
  if(require(pkg, character.only=TRUE, quietly=TRUE)) {
    version <- packageVersion(pkg)
    cat(sprintf("✅ %s (%s)\n", pkg, version))
  } else {
    cat(sprintf("❌ %s (no instalado)\n", pkg))
  }
}
cat("\n")

# 4. Estado ICFESMathExams
cat("🎯 Estado ICFESMathExams:\n")
if("ICFESMathExams" %in% installed.packages()[,1]) {
  library(ICFESMathExams)
  version <- packageVersion("ICFESMathExams")
  functions <- ls("package:ICFESMathExams")
  cat(sprintf("✅ ICFESMathExams v%s instalado\n", version))
  cat(sprintf("📊 Funciones disponibles: %d\n", length(functions)))
  
  # Test básico
  tryCatch({
    config <- crear_configuracion_basica(nivel="grado_11", areas="algebra", num_ejercicios=1, num_versiones=1)
    cat("✅ Test básico: EXITOSO\n")
  }, error = function(e) {
    cat("❌ Test básico: ERROR -", e$message, "\n")
  })
} else {
  cat("❌ ICFESMathExams no está instalado\n")
}

cat("\n🏁 Diagnóstico completado\n")
EOF

Rscript diagnostico_completo.R
```

### Test de Conectividad
```bash
# Verificar conectividad a repositorios
cat > test_conectividad.R << 'EOF'
cat("🌐 Test de Conectividad\n")
cat("======================\n\n")

repos <- c(
  "CRAN" = "https://cloud.r-project.org/",
  "RStudio" = "https://cran.rstudio.com/",
  "Berkeley" = "https://cran.cnr.berkeley.edu/"
)

for(name in names(repos)) {
  url <- repos[name]
  cat("Probando", name, "...")
  
  tryCatch({
    con <- url(paste0(url, "PACKAGES.gz"))
    close(con)
    cat(" ✅\n")
  }, error = function(e) {
    cat(" ❌\n")
  })
}
EOF

Rscript test_conectividad.R
```

## ✅ Lista de Verificación Final

Usa esta lista para asegurar que todo esté funcionando:

### ☑️ Prerrequisitos del Sistema
- [ ] Manjaro actualizado (`sudo pacman -Syu`)
- [ ] R instalado (`R --version`)
- [ ] Compiladores instalados (`gcc --version`, `gfortran --version`)
- [ ] LaTeX instalado (`pdflatex --version`)
- [ ] Bibliotecas de desarrollo (`pacman -Q libxml2 openssl`)

### ☑️ Dependencias R
- [ ] devtools instalado y funcional
- [ ] testthat disponible
- [ ] roxygen2 disponible
- [ ] knitr y rmarkdown disponibles

### ☑️ ICFESMathExams
- [ ] Paquete extraído correctamente
- [ ] Estructura verificada (`Rscript verify.R`)
- [ ] Instalación exitosa (`Rscript build.R`)
- [ ] Tests pasan (`R -e "devtools::test()"`)
- [ ] Funciones básicas funcionan

### ☑️ Funcionalidad
- [ ] Puede cargar el paquete (`library(ICFESMathExams)`)
- [ ] Puede crear configuración básica
- [ ] Puede generar datos de prueba
- [ ] Puede acceder a documentación (`browseVignettes('ICFESMathExams')`)

## 🆘 Último Recurso

Si nada funciona, usa este script de "reset completo":

```bash
#!/bin/bash
echo "🔄 Reset Completo ICFESMathExams"

# Limpiar R completamente
R -e "remove.packages(installed.packages()[,1])"

# Reinstalar R
sudo pacman -Rs r --noconfirm
sudo pacman -S r --noconfirm

# Reinstalar dependencias críticas
sudo pacman -S base-devel gcc-fortran blas lapack texlive-core

# Reinstalar ICFESMathExams desde cero
cd ~/R/ICFESMathExams/ICFESMathExams
Rscript clean.R
R -e "install.packages(c('devtools','testthat','roxygen2'), repos='https://cloud.r-project.org/')"
Rscript build.R

echo "✅ Reset completado - probar funcionalidad"
```

---

**💡 Recuerda:** La mayoría de problemas se resuelven instalando las dependencias correctas del sistema y teniendo paciencia durante la instalación de paquetes R.