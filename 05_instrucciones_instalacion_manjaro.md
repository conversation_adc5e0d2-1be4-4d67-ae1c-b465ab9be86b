# 🎯 Guía de Instalación Paso a Paso - ICFESMathExams en Manjaro Plasma

## 📥 Archivos Descargados

Has recibido **ICFESMathExams_Manjaro_Complete.tar.gz** (41KB) que contiene:

✅ **Paquete R completo** con 34 funciones  
✅ **Guías detalladas** específicas para Manjaro  
✅ **Scripts automatizados** de instalación  
✅ **Herramientas de diagnóstico** y troubleshooting  

## 🚀 Instalación en 3 Pasos Simples

### Paso 1: Extraer Archivos
```bash
# En tu directorio de descargas
cd ~/Downloads

# Extraer el paquete completo
tar -xzf ICFESMathExams_Manjaro_Complete.tar.gz
cd ICFESMathExams_Manjaro_Complete/
```

### Paso 2: Verificar Sistema
```bash
# Hacer ejecutable el verificador
chmod +x verificar_prerrequisitos_manjaro

# Ejecutar verificación
./verificar_prerrequisitos_manjaro
```

**Si hay errores**, instala lo que falte:
```bash
sudo pacman -S r gcc-fortran blas lapack texlive-core texlive-latexextra
```

### Paso 3: Instalación Automática
```bash
# Hacer ejecutable el instalador
chmod +x instalacion_automatica_manjaro

# Ejecutar instalación completa
./instalacion_automatica_manjaro
```

## ✅ Verificación Final

```bash
# Probar que funciona
R -e "
library(ICFESMathExams)
config <- crear_configuracion_basica(nivel='grado_11', areas='algebra', num_ejercicios=5, num_versiones=2)
datos <- generar_datos_icfes(config)
cat('🎉 ¡ICFESMathExams instalado exitosamente!\n')
"
```

## 📚 Próximos Pasos

### 1. Explorar Documentación
```bash
# Abrir vignettes en navegador
R -e "browseVignettes('ICFESMathExams')"
```

### 2. Ejecutar Primer Ejemplo
```bash
# El instalador crea este archivo automáticamente
Rscript ~/ejemplo_basico.R
```

### 3. Ver Funciones Disponibles
```bash
# Lista completa de funciones
R -e "help(package='ICFESMathExams')"
```

## 🔧 Si Encuentras Problemas

### Opción 1: Consultar Troubleshooting
```bash
# Abre este archivo
cat troubleshooting_manjaro.md
```

### Opción 2: Instalación Manual
```bash
# Si la automática falla, sigue esta guía
cat guia_instalacion_manjaro_completa.md
```

### Opción 3: Comandos Rápidos
```bash
# Para soluciones express
cat comandos_rapidos_manjaro.md
```

## 🎯 Casos de Uso Inmediatos

### Para Docentes
```bash
R -e "
library(ICFESMathExams)
# Crear evaluación de 20 preguntas, 3 versiones
examen <- crear_examen_personalizado(grado=11, temas=c('algebra','geometria'), num_preguntas=20, versiones=3)
exportar_resultados(examen, formato='pdf', archivo='mi_evaluacion.pdf')
"
```

### Para Coordinadores
```bash
R -e "
library(ICFESMathExams)
# Simulacro para 100 estudiantes
simulacro <- generar_simulacro_masivo(estudiantes=100, incluir_analisis=TRUE)
informe <- generar_reporte_directivo(simulacro)
"
```

### Para Investigadores
```bash
R -e "
library(ICFESMathExams)
# Análisis psicométrico avanzado
analisis <- ejecutar_analisis_irt(datos_respuestas, modelo='3PL')
reporte_cientifico <- generar_reporte_investigacion(analisis)
"
```

## 💡 Tips Adicionales

### Crear Alias Útiles
```bash
# Agregar a ~/.bashrc para acceso rápido
echo 'alias icfes="R -e \"library(ICFESMathExams)\""' >> ~/.bashrc
echo 'alias icfes-doc="R -e \"browseVignettes(\'ICFESMathExams\')\""' >> ~/.bashrc
source ~/.bashrc
```

### Backup de Configuraciones
```bash
# Crear backup después de la instalación
tar -czf icfes_backup_$(date +%Y%m%d).tar.gz ~/R/ICFESMathExams/
```

## 📊 Contenido del Paquete Instalado

Una vez instalado tendrás acceso a:

- **9 Funciones de Utilidades**: Configuración y helpers
- **8 Funciones de Generación**: Creación de datos matemáticos
- **8 Funciones de Validación**: Control de calidad pedagógica
- **4 Funciones de Testing**: Análisis estadístico y psicométrico
- **5 Funciones de Inicialización**: Configuración del paquete
- **3 Vignettes Completas**: Guías detalladas de uso
- **20+ Tests Unitarios**: Verificación automática de calidad

## 🎉 ¡Listo para Empezar!

Con esta instalación tendrás un sistema completo para:

✅ **Generar miles** de versiones únicas de exámenes  
✅ **Validar calidad** pedagógica automáticamente  
✅ **Analizar resultados** con técnicas avanzadas  
✅ **Exportar** en múltiples formatos  
✅ **Integrar** con plataformas educativas  

**🚀 ¡Tu sistema de evaluación matemática ICFES está listo para usar!**

---

*¿Tienes algún problema durante la instalación? Revisa troubleshooting_manjaro.md o consulta las guías incluidas.*