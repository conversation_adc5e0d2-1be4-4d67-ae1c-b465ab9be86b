# Comandos Rápidos: ICFESMathExams en Manjaro Plasma ⚡

## 🚀 Instalación Express (Un Solo Comando)

```bash
# Comando todo-en-uno (después de descargar ICFESMathExams_v1.0.0.tar.gz)
curl -sSL https://raw.githubusercontent.com/tuusuario/ICFESMathExams/main/install_manjaro.sh | bash
```

**O manualmente:**

```bash
# 1. Instalar prerrequisitos
sudo pacman -S --needed r gcc-fortran blas lapack texlive-core texlive-latexextra

# 2. Extraer y entrar al paquete
tar -xzf ICFESMathExams_v1.0.0.tar.gz && cd ICFESMathExams

# 3. Instalar dependencias R e ICFESMathExams
R -e "install.packages(c('devtools','testthat','roxygen2'), repos='https://cloud.r-project.org/')"
Rscript build.R
```

## ⚡ Comandos de Verificación Rápida

```bash
# Verificar que R funciona
R --version

# Verificar ICFESMathExams instalado
R -e "library(ICFESMathExams); packageVersion('ICFESMathExams')"

# Test rápido de funcionalidad
R -e "library(ICFESMathExams); config <- crear_configuracion_basica(nivel='grado_11', areas='algebra', num_ejercicios=3, num_versiones=1); cat('✅ Funciona!\n')"
```

## 🧪 Comandos de Uso Inmediato

### Crear tu primer examen:
```bash
R -e "
library(ICFESMathExams)
config <- crear_configuracion_basica(nivel='grado_11', areas=c('algebra','geometria'), num_ejercicios=10, num_versiones=5)
datos <- generar_datos_icfes(config)
validacion <- validar_datos_icfes(datos)
cat('🎉 Examen creado exitosamente!\n')
"
```

### Ver documentación:
```bash
# Abrir vignettes en navegador
R -e "browseVignettes('ICFESMathExams')"

# Ver ayuda de función específica
R -e "?crear_configuracion_basica"

# Listar todas las funciones
R -e "help(package='ICFESMathExams')"
```

### Ejecutar tests del paquete:
```bash
# Desde el directorio del paquete
cd ~/R/ICFESMathExams/ICFESMathExams
R -e "devtools::test()"

# O usando testthat directamente
R -e "testthat::test_dir('tests/testthat')"
```

## 🔧 Comandos de Troubleshooting

### Problemas con dependencias:
```bash
# Reinstalar dependencias del sistema
sudo pacman -S --needed base-devel r gcc-fortran blas lapack

# Reinstalar dependencias R
R -e "install.packages(c('devtools','testthat','roxygen2','knitr','rmarkdown'), dependencies=TRUE, repos='https://cloud.r-project.org/')"
```

### Problemas con LaTeX:
```bash
# Instalar LaTeX completo
sudo pacman -S texlive-most

# O solo lo esencial
sudo pacman -S texlive-core texlive-latexextra texlive-fontsextra
```

### Limpiar instalación:
```bash
# Limpiar paquete R
R -e "remove.packages('ICFESMathExams')"

# Limpiar archivos temporales
cd ~/R/ICFESMathExams/ICFESMathExams
Rscript clean.R

# Reinstalar desde cero
Rscript build.R
```

### Verificar errores de compilación:
```bash
# Ver capacidades de R
R -e "capabilities()"

# Verificar compilador
gcc --version
gfortran --version

# Test de compilación básica
R -e "tools:::.check_packages_used()"
```

## 📊 Comandos de Desarrollo Avanzado

### Construir documentación:
```bash
cd ~/R/ICFESMathExams/ICFESMathExams
R -e "devtools::document()"
R -e "devtools::build_vignettes()"
```

### Verificar calidad del paquete:
```bash
# Check completo del paquete
R -e "devtools::check()"

# Solo verificar estructura
Rscript verify.R

# Verificar cobertura de tests
R -e "covr::package_coverage()"
```

### Crear distribución:
```bash
# Construir tarball
R -e "devtools::build()"

# Construir binario
R -e "devtools::build(binary=TRUE)"
```

## 🎯 Comandos por Tipo de Usuario

### Para Docentes:
```bash
# Crear evaluación trimestral
R -e "
library(ICFESMathExams)
examen <- crear_examen_personalizado(grado=11, temas=c('funciones','trigonometria'), dificultad='intermedio', tiempo=90)
exportar_resultados(examen, formato='pdf', archivo='evaluacion_trimestre3.pdf')
"
```

### Para Coordinadores:
```bash
# Simulacro masivo
R -e "
library(ICFESMathExams)
simulacro <- generar_simulacro_masivo(estudiantes=200, areas='todas', incluir_analisis=TRUE)
informe <- generar_informe_directivo(simulacro)
"
```

### Para Investigadores:
```bash
# Análisis IRT
R -e "
library(ICFESMathExams)
analisis <- ejecutar_analisis_irt(datos_respuestas, modelo='3PL')
reporte <- generar_reporte_investigacion(analisis)
"
```

## 🚨 Comandos de Emergencia

### Si nada funciona:
```bash
# Reseteo completo del entorno R
R -e "remove.packages(installed.packages()[,1])"
sudo pacman -Rs r
sudo pacman -S r
```

### Si hay problemas de permisos:
```bash
# Corregir permisos
sudo chown -R $USER:$USER ~/R/
chmod -R 755 ~/R/
```

### Si hay conflictos de versiones:
```bash
# Verificar versiones instaladas
pacman -Q | grep -E 'r |gcc|texlive'
R -e "sessionInfo()"
```

## 💡 Tips de Productividad

### Aliases útiles para ~/.bashrc:
```bash
echo 'alias icfes="R -e \"library(ICFESMathExams)\""' >> ~/.bashrc
echo 'alias icfes-doc="R -e \"browseVignettes(\'ICFESMathExams\')\""' >> ~/.bashrc
echo 'alias icfes-test="cd ~/R/ICFESMathExams/ICFESMathExams && R -e \"devtools::test()\""' >> ~/.bashrc
source ~/.bashrc
```

### Script de backup:
```bash
# Crear backup de configuraciones
tar -czf icfes_backup_$(date +%Y%m%d).tar.gz ~/R/ICFESMathExams/
```

---

## 📞 Referencias Rápidas

| Comando | Descripción |
|---------|-------------|
| `R -e "library(ICFESMathExams)"` | Cargar paquete |
| `Rscript build.R` | Reinstalar paquete |
| `Rscript verify.R` | Verificar integridad |
| `Rscript clean.R` | Limpiar temporales |
| `R -e "browseVignettes('ICFESMathExams')"` | Ver documentación |
| `R -e "devtools::test()"` | Ejecutar tests |
| `sudo pacman -S r gcc-fortran` | Instalar prerrequisitos |

**🎯 Con estos comandos tendrás ICFESMathExams funcionando en minutos!** ⚡